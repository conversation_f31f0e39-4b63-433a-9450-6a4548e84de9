# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: da0d9209ca76bde579f2da330aeb9df62b6319c834fa7baae052021b0462401f
      url: "https://pub.dev"
    source: hosted
    version: "85.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "3394478cd571a8dbbe130a35e10bd365a2a8e7a867308142f7fd20336a08c3be"
      url: "https://pub.dev"
    source: hosted
    version: "7.5.1"
  arabic_font:
    dependency: "direct main"
    description:
      name: arabic_font
      sha256: cfdf74e27f7c9f6228592c658344b24493f9b505920d9faf4d0a9c118552e61f
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: d2872f9c19731c2e5f10444b14686eb7cc85c76274bd6c16e1816bff9a3bab63
      url: "https://pub.dev"
    source: hosted
    version: "2.12.0"
  barcode:
    dependency: transitive
    description:
      name: barcode
      sha256: "7b6729c37e3b7f34233e2318d866e8c48ddb46c1f7ad01ff7bb2a8de1da2b9f4"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.9"
  bidi:
    dependency: transitive
    description:
      name: bidi
      sha256: "77f475165e94b261745cf1032c751e2032b8ed92ccb2bf5716036db79320637d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.13"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "51dc711996cbf609b90cbe5b335bbce83143875a9d58e4b5c6d3c4f684d3dda7"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "4ae2de3e1e67ea270081eaee972e1bd8f027d459f249e0f1186730784c2e7e33"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "8e928697a82be082206edb0b9c99c5a4ad6bc31c9e9b8b2f291ae65cd4a25daa"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: ee4257b3f20c0c90e72ed2b57ad637f694ccba48839a821e87db762548c22a62
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "382a4d649addbfb7ba71a3631df0ec6a45d5ab9b098638144faf27f02778eb53"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "85fbbb1036d576d966332a3f5ce83f2ce66a40bea1a94ad2d5fc29a19a0d3792"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.2"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "082001b5c3dc495d4a42f1d5789990505df20d8547d42507c29050af6933ee27"
      url: "https://pub.dev"
    source: hosted
    version: "8.10.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: "051849e2bd7c7b3bc5844ea0d096609ddc3a859890ec3a9ac4a65a2620cc1f99"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  console:
    dependency: transitive
    description:
      name: console
      sha256: e04e7824384c5b39389acdd6dc7d33f3efe6b232f6f16d7626f194f6a01ad69a
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: "direct main"
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  csv:
    dependency: "direct main"
    description:
      name: csv
      sha256: "63ed2871dd6471193dffc52c0e6c76fb86269c00244d244297abbb355c84a86e"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dart_quill_delta:
    dependency: transitive
    description:
      name: dart_quill_delta
      sha256: bddb0b2948bd5b5a328f1651764486d162c59a8ccffd4c63e8b2c5e44be1dac4
      url: "https://pub.dev"
    source: hosted
    version: "10.8.3"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "5b236382b47ee411741447c1f1e111459c941ea1b3f2b540dde54c210a3662af"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  data_table_2:
    dependency: "direct main"
    description:
      name: data_table_2
      sha256: b8dd157e4efe5f2beef092c9952a254b2192cf76a26ad1c6aa8b06c8b9d665da
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  desktop_drop:
    dependency: "direct main"
    description:
      name: desktop_drop
      sha256: bd21017e0415632c85f6b813c846bc8c9811742507776dcf6abf91a14d946e98
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: "98f28b42168cc509abc92f88518882fd58061ea372d7999aecc424345c7bff6a"
      url: "https://pub.dev"
    source: hosted
    version: "11.5.0"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: e1ea89119e34903dca74b883d0dd78eb762814f97fb6c76f35e9ff74d261a18f
      url: "https://pub.dev"
    source: hosted
    version: "7.0.3"
  diff_match_patch:
    dependency: transitive
    description:
      name: diff_match_patch
      sha256: "2efc9e6e8f449d0abe15be240e2c2a3bcd977c8d126cfd70598aee60af35c0a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  emoji_picker_flutter:
    dependency: "direct main"
    description:
      name: emoji_picker_flutter
      sha256: "7c6681783e06710608df27be0e38aa4ba73ca1ccac370bb0e7a1320723ae4bca"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  excel:
    dependency: "direct main"
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "6a95e56b2449df2273fd8c45a662d6947ce1ebb7aafe80e550a3f68297f3cacc"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: ab13ae8ef5580a411c458d6207b6774a6c237d77ac37011b13994879f68a8810
      url: "https://pub.dev"
    source: hosted
    version: "8.3.7"
  file_saver:
    dependency: "direct main"
    description:
      name: file_saver
      sha256: "017a127de686af2d2fbbd64afea97052d95f2a0f87d19d25b87e097407bf9c1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.14"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "8c9250b2bd2d8d4268e39c82543bacbaca0fda7d29e0728c3c4bbb7c820fd711"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+4"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_animation_progress_bar:
    dependency: "direct main"
    description:
      name: flutter_animation_progress_bar
      sha256: "85f05404faeef7141b1c5b53571293fc19359c120c4aea4430b2b7d808461d09"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  flutter_calendar_carousel:
    dependency: "direct main"
    description:
      name: flutter_calendar_carousel
      sha256: "9661d9079da4c93cbea935db141c79722260d676a2fee122da93740f1334bf0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  flutter_colorpicker:
    dependency: "direct main"
    description:
      name: flutter_colorpicker
      sha256: "969de5f6f9e2a570ac660fb7b501551451ea2a1ab9e2097e89475f60e07816ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_html_to_pdf:
    dependency: "direct main"
    description:
      name: flutter_html_to_pdf
      sha256: "4bec44685f62a404bac7c9e412da0fbcd389ea5eceb397c268d829ecfa148c34"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_temp_fork:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_temp_fork
      sha256: e3d02900640fbc1129245540db16944a0898b8be81694f4bf04b6c985bed9048
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: "526faf84284b86a4cb36d20a5e45147747b7563d921373d4ee0559c54fcdbcea"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "5398f14efa795ffb7a33e9b6a08798b26a180edac4ad7db3f231e40f82ce11e1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: "026cf3deef656576588449adf736a12819072f3b529007e46f847ed73199bb6a"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: f948e346c12f8d5480d2825e03de228d0eb8c3a737e4cdaa122267b89c022b5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.28"
  flutter_quill:
    dependency: "direct main"
    description:
      name: flutter_quill
      sha256: "6274834823e61291c0cedee9dd7f73fc7836ea07a12596de8f5fa08598b5eb74"
      url: "https://pub.dev"
    source: hosted
    version: "10.8.5"
  flutter_quill_delta_from_html:
    dependency: transitive
    description:
      name: flutter_quill_delta_from_html
      sha256: "4597bd0853a704696837aa6b05cffd851f587b176204c234edddfed1c1862a09"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: ab7dbb16f783307c9d7762ede2593ce32c220ba2ba0fd540a3db8e9a3acba71a
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: "6cb9fb6e5928b58b9a84bdf85012d757fd07aab8215c5205337021c4999bad27"
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: fcb1760a50d7500deca37c9a666785c047139b5f9ee15aa5469fae7dbbe3170d
      url: "https://pub.dev"
    source: hosted
    version: "4.6.2"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: dbdd8789d5aaf14cf69f74d4925ad1336b4433a6efdf2fce91e8955dc921bf22
      url: "https://pub.dev"
    source: hosted
    version: "2.3.13"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "30cb64f0b9adcc0fb36f628b4ebf4f731a2961a0ebd849f4b56200205056fe67"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.6"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "49d8f846ebeb5e2b6641fe477a7e97e5dd73f03cbfef3fd5c42177b7300fb0ed"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "175435404d20278ffd220de83c2ca293b73db95eafbdc8131fe8609be1421eb6"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: c79eeb4339f1f3deffd9ec912f8a923834bec55f7b49c9e882b8fef2c139d425
      url: "https://pub.dev"
    source: hosted
    version: "4.7.2"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: d85128a5dae4ea777324730dc65edd9c9f43155c109d5cc0a69cab74139fbac1
      url: "https://pub.dev"
    source: hosted
    version: "7.7.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "6d1264f2dffa1b1101c25a91dff0dc2daee4c18e87cd8538729773c073dbf602"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.6"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: "2c11f3f94c687ee9bad77c171151672986360b2b001d109814ee7140b2cf261b"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "317a5d961cec5b34e777b9252393f2afbd23084aa6e60fcf601dcf6341b9ebeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+23"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "34a65f6740df08bbbeb0a1abd8e6d32107941fd4868f67a507b25601651022c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "53385261521cc4a0c4658fd0ad07a7d14591cf8fc33abbceae306ddb974888dc"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: c50ef5fc083d5b5e12eef489503ba3bf5ccc899e487d691584699b4bdefeea8c
      url: "https://pub.dev"
    source: hosted
    version: "6.9.5"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: c35baad643ba394b40aac41080300150a4f08fd0fd6a10378f8f7c6bc161acec
      url: "https://pub.dev"
    source: hosted
    version: "10.0.8"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: c35bb79562d980e9a453fc715854e1ed39e24e7d0297a880ef54e17f9874a9d7
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mockito:
    dependency: "direct main"
    description:
      name: mockito
      sha256: "4546eac99e8967ea91bae633d2ca7698181d008e95fa4627330cf903d573277a"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.6"
  msix:
    dependency: "direct main"
    description:
      name: msix
      sha256: edde648a8133bf301883c869d19d127049683037c65ff64173ba526ac7a8af2f
      url: "https://pub.dev"
    source: hosted
    version: "3.16.9"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  open_file:
    dependency: "direct main"
    description:
      name: open_file
      sha256: d17e2bddf5b278cb2ae18393d0496aa4f162142ba97d1a9e0c30d476adf99c0e
      url: "https://pub.dev"
    source: hosted
    version: "3.5.10"
  open_file_android:
    dependency: transitive
    description:
      name: open_file_android
      sha256: "58141fcaece2f453a9684509a7275f231ac0e3d6ceb9a5e6de310a7dff9084aa"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  open_file_ios:
    dependency: transitive
    description:
      name: open_file_ios
      sha256: "02996f01e5f6863832068e97f8f3a5ef9b613516db6897f373b43b79849e4d07"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_linux:
    dependency: transitive
    description:
      name: open_file_linux
      sha256: d189f799eecbb139c97f8bc7d303f9e720954fa4e0fa1b0b7294767e5f2d7550
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  open_file_mac:
    dependency: transitive
    description:
      name: open_file_mac
      sha256: "1440b1e37ceb0642208cfeb2c659c6cda27b25187a90635c9d1acb7d0584d324"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_platform_interface:
    dependency: transitive
    description:
      name: open_file_platform_interface
      sha256: "101b424ca359632699a7e1213e83d025722ab668b9fd1412338221bf9b0e5757"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_web:
    dependency: transitive
    description:
      name: open_file_web
      sha256: e3dbc9584856283dcb30aef5720558b90f88036360bd078e494ab80a80130c4f
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  open_file_windows:
    dependency: transitive
    description:
      name: open_file_windows
      sha256: d26c31ddf935a94a1a3aa43a23f4fff8a5ff4eea395fe7a8cb819cf55431c875
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7976bfe4c583170d6cdc7077e3237560b364149fcd268b5f53d95a991963b191"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "6c935fb612dff8e3cc9632c2b301720c77450a126114126ffaafe28d2e87956c"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: d0d310befe2c8ab9e7f393288ccbb11b60c019c6b5afc21973eeee4dda2b35e9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pdf:
    dependency: "direct main"
    description:
      name: pdf
      sha256: "28eacad99bffcce2e05bba24e50153890ad0255294f4dd78a17075a2ba5c8416"
      url: "https://pub.dev"
    source: hosted
    version: "3.11.3"
  pdf_widget_wrapper:
    dependency: transitive
    description:
      name: pdf_widget_wrapper
      sha256: c930860d987213a3d58c7ec3b7ecf8085c3897f773e8dc23da9cae60a5d6d0f5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  percent_indicator:
    dependency: "direct main"
    description:
      name: percent_indicator
      sha256: "157d29133bbc6ecb11f923d36e7960a96a3f28837549a20b65e5135729f0f9fd"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.5"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  photo_view:
    dependency: "direct main"
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pluto_grid:
    dependency: "direct main"
    description:
      name: pluto_grid
      sha256: "1d4cd9d2652742b556aa9b3230cc64672a3f63c34a9acc80fef794ab36ad903b"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  printing:
    dependency: "direct main"
    description:
      name: printing
      sha256: "482cd5a5196008f984bb43ed0e47cbfdca7373490b62f3b27b3299275bf22a93"
      url: "https://pub.dev"
    source: hosted
    version: "5.14.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "0560ba233314abbed0a48a2956f7f022cce7c3e1e73df540277da7544cad4082"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5a1d2586170e172b8a8c8470bbbffd5eb0cd38a66c0d77155ea138d3af3a4445"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  quill_native_bridge:
    dependency: transitive
    description:
      name: quill_native_bridge
      sha256: "0b3200c57bb4f1f12d6c764648d42482891f20f12024c75fe3479cafc1e132c9"
      url: "https://pub.dev"
    source: hosted
    version: "10.7.11"
  quill_native_bridge_android:
    dependency: transitive
    description:
      name: quill_native_bridge_android
      sha256: b75c7e6ede362a7007f545118e756b1f19053994144ec9eda932ce5e54a57569
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1+2"
  quill_native_bridge_ios:
    dependency: transitive
    description:
      name: quill_native_bridge_ios
      sha256: d23de3cd7724d482fe2b514617f8eedc8f296e120fb297368917ac3b59d8099f
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  quill_native_bridge_linux:
    dependency: transitive
    description:
      name: quill_native_bridge_linux
      sha256: "5fcc60cab2ab9079e0746941f05c5ca5fec85cc050b738c8c8b9da7c09da17eb"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  quill_native_bridge_macos:
    dependency: transitive
    description:
      name: quill_native_bridge_macos
      sha256: "1c0631bd1e2eee765a8b06017c5286a4e829778f4585736e048eb67c97af8a77"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  quill_native_bridge_platform_interface:
    dependency: transitive
    description:
      name: quill_native_bridge_platform_interface
      sha256: "8264a2bdb8a294c31377a27b46c0f8717fa9f968cf113f7dc52d332ed9c84526"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2+1"
  quill_native_bridge_web:
    dependency: transitive
    description:
      name: quill_native_bridge_web
      sha256: "7c723f6824b0250d7f33e8b6c23f2f8eb0103fe48ee7ebf47ab6786b64d5c05d"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  quill_native_bridge_windows:
    dependency: transitive
    description:
      name: quill_native_bridge_windows
      sha256: "60e50d74238f22ceb43113d9a42b6627451dab9fc27f527b979a32051cf1da45"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "6e8bf70b7fef813df4e9a36f658ac46d107db4b4cfe1048b477d4e453a8159f5"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "20cbd561f743a342c76c151d6ddb93a9ce6005751e7aa458baad3858bfbfb6ac"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: e7dd780a7ffb623c57850b33f43309312fc863fb6aa3d276a754bb299839ef12
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "3632775c8e90d6c9712f883e633716432a27758216dfb61bd86a8321c0580925"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  signalr_netcore:
    dependency: "direct main"
    description:
      name: signalr_netcore
      sha256: "02d2e399c5a4b5587dfaa0a1da23502f7f438e8b92ac20bc5e652a796600d11a"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.7+2-nullsafety.3"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      sha256: ba2cd5af24ff20a0b8d609cec3f40e5b0744d2a71804a2616ae086b9c19d19a3
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "35c8150ece9e8c8d263337a265153c3329667640850b9304861faea59fc98f6b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "86d247119aedce8e63f4751bd9626fc9613255935558447569ad42f9f5b48b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.5"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  syncfusion_flutter_charts:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_charts
      sha256: c43329a0f74337c96cbf6e1993c990a111c56df4f307b63590063c50d7b027f1
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: bee87cdfe527b31705190162e3bd27bf468d591ae7c68182244bdfd94e21f737
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_flutter_pdf:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdf
      sha256: "08fc998934dab953e980ab3b98482e994beda2e736b88c9fec99cdcf04036509"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_flutter_pdfviewer:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdfviewer
      sha256: "240c3c50e5a9a0ed2ca9d0449414f83ddcb09dafc798362bf8d8db11dabffdfe"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_flutter_signaturepad:
    dependency: transitive
    description:
      name: syncfusion_flutter_signaturepad
      sha256: "903dcd8a6afcd94488be296f9e45c28e2ea6634849eccb70066e310db4198edc"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_pdfviewer_macos:
    dependency: transitive
    description:
      name: syncfusion_pdfviewer_macos
      sha256: "529befb03610bc5f647ddba84150355bea18a25f5ce8329f3065971a498b89d1"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_pdfviewer_platform_interface:
    dependency: transitive
    description:
      name: syncfusion_pdfviewer_platform_interface
      sha256: e27a16b987b7b0d241a153ac96119c2bf753075cae93301e0143ebf7c139503b
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_pdfviewer_web:
    dependency: transitive
    description:
      name: syncfusion_pdfviewer_web
      sha256: "1cf1d8f8871fd421e454e7e38f1044b6b170bb0201a5bee8634210d27aa79e2e"
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  syncfusion_pdfviewer_windows:
    dependency: transitive
    description:
      name: syncfusion_pdfviewer_windows
      sha256: cc6826cdae73657d010bd4480b5c6f06a78589e86185eeec9f6ec931c4c4276a
      url: "https://pub.dev"
    source: hosted
    version: "29.2.11"
  table_calendar:
    dependency: "direct main"
    description:
      name: table_calendar
      sha256: b2896b7c86adf3a4d9c911d860120fe3dbe03c85db43b22fd61f14ee78cdbb63
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  timeline_tile:
    dependency: "direct main"
    description:
      name: timeline_tile
      sha256: "85ec2023c67137397c2812e3e848b2fb20b410b67cd9aff304bb5480c376fc0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "8582d7f6fe14d2652b4c45c9b6c14c0b678c2af2d083a11b604caeba51930d79"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.16"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "4bd2b7b4dc4d4d0b94e5babfffbca8eac1a126c7f3d6ecbc1a11013faa3abba2"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "0968250880a6c5fe7edc067ed0a13d4bae1577fe2771dcf3010d52c4a9d3ca14"
      url: "https://pub.dev"
    source: hosted
    version: "14.3.1"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "0b7fd4a0bbc4b92641dbf20adfd7e3fd1398fe17102d94b674234563e110088a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web_socket_channel:
    dependency: "direct main"
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "329edf97fdd893e0f1e3b9e88d6a0e627128cc17cc316a8d67fda8f1451178ba"
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "6f1b564492d0147b330dd794fee8f512cec4977957f310f9951b5f9d83618dae"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
sdks:
  dart: ">=3.7.0 <3.8.0"
  flutter: ">=3.29.0"
