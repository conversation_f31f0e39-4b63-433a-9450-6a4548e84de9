import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:get/get.dart';
import '../../models/permission_models.dart';

class CreateRoleDialog {
  /// حوار موحد لإنشاء أو تعديل دور
  static void show(BuildContext context, NewPermissionsController controller, {Permission? role}) {
    final nameController = TextEditingController(text: role?.name ?? '');
    final descriptionController = TextEditingController(text: role?.description ?? '');
    
    Get.dialog(
      AlertDialog(
        title: Text(role == null ? 'إنشاء دور جديد' : 'تعديل الدور'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الدور *',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الدور',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            child: Text(role == null ? 'إنشاء' : 'حفظ التعديلات'),
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يجب إدخال اسم الدور',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                );
                return;
              }

              Get.back();
              if (role == null) {
                // إنشاء جديد
                final newPermission = Permission(
                  id: 0,
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim().isEmpty ? null : descriptionController.text.trim(),
                  permissionGroup: 'CustomRoles',
                  createdAt: DateTime.now().millisecondsSinceEpoch,
                  level: 1,
                  isDefault: false,
                );
                final createSuccess = await controller.createPermission(newPermission);
                if (createSuccess) {
                  Get.snackbar(
                    'تم بنجاح',
                    'تم إنشاء الدور بنجاح',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                  );
                }
              } else {
                // تعديل موجود
                final updatedPermission = role.copyWith(
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim().isEmpty ? null : descriptionController.text.trim(),
                );
                final updateSuccess = await controller.updatePermission(role.id, updatedPermission);
                if (updateSuccess) {
                  Get.snackbar(
                    'تم التعديل',
                    'تم تعديل الدور بنجاح',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }
}