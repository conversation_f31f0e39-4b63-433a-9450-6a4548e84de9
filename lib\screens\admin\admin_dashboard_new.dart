import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/admin_controller.dart';
import '../../services/unified_permission_service.dart';
import 'shared/admin_card_widget.dart';
import 'users/user_management_screen.dart';
import 'roles/role_management_screen.dart';
import 'permissions/permission_management_screen.dart';
import 'system/system_settings_screen.dart';

/// لوحة التحكم الإدارية المحسنة
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final AuthController _authController = Get.find<AuthController>();
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    try {
      await _adminController.loadUsers();
      await _adminController.loadRoles();
      await _adminController.loadPermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات لوحة التحكم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم الإدارية'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // معلومات المستخدم الحالي
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Obx(() {
              final user = _authController.currentUser.value;
              return Row(
                children: [
                  Text(
                    user?.name ?? 'مستخدم',
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(width: 8),
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.white,
                    child: Text(
                      user?.name?.substring(0, 1) ?? 'م',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات سريعة
              _buildStatsSection(),
              
              const SizedBox(height: 24),
              
              // قسم إدارة المستخدمين
              _buildUsersSection(),
              
              const SizedBox(height: 16),
              
              // قسم إدارة الأدوار والصلاحيات
              _buildRolesPermissionsSection(),
              
              const SizedBox(height: 16),
              
              // قسم إدارة النظام
              _buildSystemSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          return Row(
            children: [
              Expanded(
                child: AdminStatsCard(
                  title: 'المستخدمين',
                  value: '${_adminController.users.length}',
                  icon: Icons.people,
                  color: Colors.blue,
                  subtitle: 'إجمالي المستخدمين',
                  onTap: _permissionService.canViewAllUsers() 
                    ? () => Get.to(() => const UserManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الأدوار',
                  value: '${_adminController.roles.length}',
                  icon: Icons.security,
                  color: Colors.green,
                  subtitle: 'الأدوار النشطة',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const RoleManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الصلاحيات',
                  value: '${_adminController.permissions.length}',
                  icon: Icons.vpn_key,
                  color: Colors.orange,
                  subtitle: 'إجمالي الصلاحيات',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const PermissionManagementScreen())
                    : null,
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء قسم إدارة المستخدمين
  Widget _buildUsersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة المستخدمين',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة المستخدمين',
          subtitle: 'إضافة وتعديل وحذف المستخدمين',
          icon: Icons.people_outline,
          isEnabled: _permissionService.canViewAllUsers(),
          onTap: () => Get.to(() => const UserManagementScreen()),
        ),
      ],
    );
  }

  /// بناء قسم إدارة الأدوار والصلاحيات
  Widget _buildRolesPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأدوار والصلاحيات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة الأدوار',
          subtitle: 'إنشاء وتعديل الأدوار الافتراضية والمخصصة',
          icon: Icons.admin_panel_settings,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: () => Get.to(() => const RoleManagementScreen()),
        ),
        AdminCardWidget(
          title: 'إدارة الصلاحيات',
          subtitle: 'منح وسحب الصلاحيات للمستخدمين والأدوار',
          icon: Icons.security,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: () => Get.to(() => const PermissionManagementScreen()),
        ),
      ],
    );
  }

  /// بناء قسم إدارة النظام
  Widget _buildSystemSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة النظام',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إعدادات النظام',
          subtitle: 'إعدادات عامة وأمان النظام',
          icon: Icons.settings,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => Get.to(() => const SystemSettingsScreen()),
        ),
        AdminCardWidget(
          title: 'النسخ الاحتياطية',
          subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
          icon: Icons.backup,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () {
            // TODO: إضافة شاشة النسخ الاحتياطية
            Get.snackbar(
              'قريباً',
              'ستتوفر هذه الميزة قريباً',
              snackPosition: SnackPosition.BOTTOM,
            );
          },
        ),
        AdminCardWidget(
          title: 'التقارير والإحصائيات',
          subtitle: 'تقارير النشاط والاستخدام',
          icon: Icons.analytics,
          isEnabled: _permissionService.canViewReports(),
          onTap: () {
            // TODO: إضافة شاشة التقارير
            Get.snackbar(
              'قريباً',
              'ستتوفر هذه الميزة قريباً',
              snackPosition: SnackPosition.BOTTOM,
            );
          },
        ),
      ],
    );
  }
}
