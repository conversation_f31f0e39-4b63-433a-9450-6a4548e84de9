import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/admin_controller.dart';
import '../../services/unified_permission_service.dart';
import 'shared/admin_card_widget.dart';
import 'shared/admin_dialog_widget.dart';
import 'users/user_management_screen.dart';
import 'roles/role_management_screen.dart';
import 'permissions/permission_management_screen.dart';
import 'system/system_settings_screen.dart';

/// لوحة التحكم الإدارية المحسنة
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final AuthController _authController = Get.find<AuthController>();
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    try {
      await _adminController.loadUsers();
      await _adminController.loadRoles();
      await _adminController.loadPermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات لوحة التحكم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم الإدارية'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // معلومات المستخدم الحالي
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Obx(() {
              final user = _authController.currentUser.value;
              return Row(
                children: [
                  Text(
                    user?.name ?? 'مستخدم',
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(width: 8),
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.white,
                    child: Text(
                      user?.name?.substring(0, 1) ?? 'م',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات سريعة
              _buildStatsSection(),
              
              const SizedBox(height: 24),
              
              // قسم إدارة المستخدمين
              _buildUsersSection(),
              
              const SizedBox(height: 16),
              
              // قسم إدارة الأدوار والصلاحيات
              _buildRolesPermissionsSection(),
              
              const SizedBox(height: 16),
              
              // قسم إدارة النظام
              _buildSystemSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          return Row(
            children: [
              Expanded(
                child: AdminStatsCard(
                  title: 'المستخدمين',
                  value: '${_adminController.users.length}',
                  icon: Icons.people,
                  color: Colors.blue,
                  subtitle: 'إجمالي المستخدمين',
                  onTap: _permissionService.canViewAllUsers() 
                    ? () => Get.to(() => const UserManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الأدوار',
                  value: '${_adminController.roles.length}',
                  icon: Icons.security,
                  color: Colors.green,
                  subtitle: 'الأدوار النشطة',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const RoleManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الصلاحيات',
                  value: '${_adminController.permissions.length}',
                  icon: Icons.vpn_key,
                  color: Colors.orange,
                  subtitle: 'إجمالي الصلاحيات',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const PermissionManagementScreen())
                    : null,
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء قسم إدارة المستخدمين
  Widget _buildUsersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة المستخدمين',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة المستخدمين',
          subtitle: 'إضافة وتعديل وحذف المستخدمين',
          icon: Icons.people_outline,
          isEnabled: _permissionService.canViewAllUsers(),
          onTap: () => Get.to(() => const UserManagementScreen()),
        ),
      ],
    );
  }

  /// بناء قسم إدارة الأدوار والصلاحيات
  Widget _buildRolesPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأدوار والصلاحيات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة الأدوار',
          subtitle: 'إنشاء وتعديل الأدوار الافتراضية والمخصصة',
          icon: Icons.admin_panel_settings,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: () => Get.to(() => const RoleManagementScreen()),
        ),
        AdminCardWidget(
          title: 'إدارة الصلاحيات',
          subtitle: 'منح وسحب الصلاحيات للمستخدمين والأدوار',
          icon: Icons.security,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: () => Get.to(() => const PermissionManagementScreen()),
        ),
      ],
    );
  }

  /// بناء قسم إدارة النظام
  Widget _buildSystemSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة النظام',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إعدادات النظام',
          subtitle: 'إعدادات عامة وأمان النظام',
          icon: Icons.settings,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => Get.to(() => const SystemSettingsScreen()),
        ),
        AdminCardWidget(
          title: 'النسخ الاحتياطية',
          subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
          icon: Icons.backup,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showBackupManagement(),
        ),
        AdminCardWidget(
          title: 'التقارير والإحصائيات',
          subtitle: 'تقارير النشاط والاستخدام',
          icon: Icons.analytics,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showReportsManagement(),
        ),
      ],
    );
  }

  /// عرض إدارة النسخ الاحتياطية
  void _showBackupManagement() {
    Get.dialog(
      AlertDialog(
        title: const Text('إدارة النسخ الاحتياطية'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(() {
                if (_adminController.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.backup, color: Colors.blue),
                      title: const Text('إنشاء نسخة احتياطية جديدة'),
                      subtitle: const Text('إنشاء نسخة احتياطية من البيانات الحالية'),
                      onTap: () => _createBackup(),
                    ),
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.restore, color: Colors.orange),
                      title: const Text('استعادة نسخة احتياطية'),
                      subtitle: const Text('استعادة البيانات من نسخة احتياطية سابقة'),
                      onTap: () => _restoreBackup(),
                    ),
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.list, color: Colors.green),
                      title: const Text('عرض النسخ الاحتياطية'),
                      subtitle: Text('${_adminController.backups.length} نسخة متاحة'),
                      onTap: () => _showBackupsList(),
                    ),
                  ],
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إدارة التقارير
  void _showReportsManagement() {
    Get.dialog(
      AlertDialog(
        title: const Text('التقارير والإحصائيات'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.analytics, color: Colors.blue),
                title: const Text('تقارير النشاط'),
                subtitle: const Text('تقارير نشاط المستخدمين والنظام'),
                onTap: () => _showActivityReports(),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.bar_chart, color: Colors.green),
                title: const Text('إحصائيات الاستخدام'),
                subtitle: const Text('إحصائيات تفصيلية عن استخدام النظام'),
                onTap: () => _showUsageStatistics(),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.timeline, color: Colors.orange),
                title: const Text('تقارير الأداء'),
                subtitle: const Text('تقارير أداء النظام والمستخدمين'),
                onTap: () => _showPerformanceReports(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  void _createBackup() async {
    Get.back(); // إغلاق الحوار الحالي

    final confirmed = await AdminConfirmDialog.show(
      title: 'إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية جديدة من البيانات؟',
      confirmText: 'إنشاء',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنشاء النسخة الاحتياطية...');

      try {
        await _adminController.loadBackups();
        Get.back(); // إغلاق حوار التحميل

        Get.snackbar(
          'نجح',
          'تم إنشاء النسخة الاحتياطية بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.back(); // إغلاق حوار التحميل

        Get.snackbar(
          'خطأ',
          'فشل في إنشاء النسخة الاحتياطية: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup() async {
    Get.back(); // إغلاق الحوار الحالي

    final confirmed = await AdminConfirmDialog.show(
      title: 'استعادة نسخة احتياطية',
      message: 'تحذير: ستؤدي هذه العملية إلى استبدال البيانات الحالية. هل تريد المتابعة؟',
      confirmText: 'استعادة',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      Get.snackbar(
        'قيد التطوير',
        'ستتوفر هذه الميزة في التحديث القادم',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// عرض قائمة النسخ الاحتياطية
  void _showBackupsList() {
    Get.back(); // إغلاق الحوار الحالي

    Get.snackbar(
      'قيد التطوير',
      'ستتوفر هذه الميزة في التحديث القادم',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض تقارير النشاط
  void _showActivityReports() {
    Get.back(); // إغلاق الحوار الحالي

    Get.snackbar(
      'قيد التطوير',
      'ستتوفر هذه الميزة في التحديث القادم',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض إحصائيات الاستخدام
  void _showUsageStatistics() {
    Get.back(); // إغلاق الحوار الحالي

    Get.dialog(
      AlertDialog(
        title: const Text('إحصائيات الاستخدام'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            final stats = _adminController.statistics;
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow('إجمالي المستخدمين', '${stats['totalUsers'] ?? 0}'),
                _buildStatRow('المستخدمين النشطين', '${stats['activeUsers'] ?? 0}'),
                _buildStatRow('إجمالي الأدوار', '${stats['totalRoles'] ?? 0}'),
                _buildStatRow('الأدوار النشطة', '${stats['activeRoles'] ?? 0}'),
                _buildStatRow('إجمالي الصلاحيات', '${stats['totalPermissions'] ?? 0}'),
                const Divider(),
                Text(
                  'آخر تحديث: ${DateTime.fromMillisecondsSinceEpoch(stats['lastUpdated'] ?? 0).toString().split('.')[0]}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تقارير الأداء
  void _showPerformanceReports() {
    Get.back(); // إغلاق الحوار الحالي

    Get.snackbar(
      'قيد التطوير',
      'ستتوفر هذه الميزة في التحديث القادم',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
