// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import '../../routes/app_routes.dart';

// /// شاشة اختبار للوحة التحكم الإدارية المحسنة
// class TestEnhancedAdmin extends StatelessWidget {
//   const TestEnhancedAdmin({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('اختبار لوحة التحكم المحسنة'),
//         backgroundColor: Theme.of(context).colorScheme.primaryContainer,
//       ),
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.admin_panel_settings_rounded,
//               size: 100,
//               color: Theme.of(context).colorScheme.primary,
//             ),
//             const SizedBox(height: 24),
//             Text(
//               'اختبار لوحة التحكم الإدارية المحسنة',
//               style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                 fontWeight: FontWeight.bold,
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'اضغط على الزر أدناه للانتقال إلى لوحة التحكم الجديدة',
//               style: Theme.of(context).textTheme.bodyLarge,
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 32),
            
//             // أزرار الاختبار
//             Wrap(
//               spacing: 16,
//               runSpacing: 16,
//               alignment: WrapAlignment.center,
//               children: [
//                 FilledButton.icon(
//                   onPressed: () => Get.toNamed(AppRoutes.enhancedAdmin),
//                   icon: const Icon(Icons.dashboard_rounded),
//                   label: const Text('لوحة التحكم المحسنة'),
//                 ),
//                 FilledButton.tonalIcon(
//                   onPressed: () => Get.toNamed(AppRoutes.admin),
//                   icon: const Icon(Icons.admin_panel_settings_rounded),
//                   label: const Text('لوحة التحكم القديمة'),
//                 ),
//               ],
//             ),
            
//             const SizedBox(height: 32),
            
//             // معلومات النظام
//             Card(
//               margin: const EdgeInsets.all(16),
//               child: Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'معلومات النظام الجديد:',
//                       style: Theme.of(context).textTheme.titleMedium?.copyWith(
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     const Text('✅ تصميم Material Design 3'),
//                     const Text('✅ واجهة متجاوبة'),
//                     const Text('✅ مكونات قابلة لإعادة الاستخدام'),
//                     const Text('✅ إدارة محسنة للحالة'),
//                     const Text('✅ خدمة API موحدة'),
//                     const Text('✅ معالجة أفضل للأخطاء'),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }