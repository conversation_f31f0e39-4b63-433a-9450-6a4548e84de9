import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/task_type_controller.dart';
import '../../models/task_type_models.dart';
import '../../constants/app_styles.dart';

/// شاشة إدارة أنواع المهام
///
/// توفر واجهة لإدارة أنواع المهام في النظام
class TaskTypeManagementTab extends StatefulWidget {
  const TaskTypeManagementTab({super.key});

  @override
  State<TaskTypeManagementTab> createState() => _TaskTypeManagementTabState();
}

class _TaskTypeManagementTabState extends State<TaskTypeManagementTab> {
  late TaskTypeController _taskTypeController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _colorController = TextEditingController();
  final _iconController = TextEditingController();
  
  TaskType? _selectedTaskType;
  bool _showInactiveTypes = false;

  @override
  void initState() {
    super.initState();
    
    // التحقق من وجود المتحكم أو إنشاؤه
    if (!Get.isRegistered<TaskTypeController>()) {
      Get.put(TaskTypeController());
    }
    _taskTypeController = Get.find<TaskTypeController>();
    
    // تحميل البيانات
    _loadData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _colorController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _taskTypeController.loadTaskTypes(includeInactive: _showInactiveTypes);
  }

  /// إعادة تعيين نموذج الإدخال
  void _resetForm() {
    _selectedTaskType = null;
    _nameController.clear();
    _descriptionController.clear();
    _colorController.clear();
    _iconController.clear();
    _formKey.currentState?.reset();
  }

  /// عرض مربع حوار إنشاء/تعديل نوع مهمة
  void _showTaskTypeDialog({TaskType? taskType}) {
    _selectedTaskType = taskType;
    
    if (taskType != null) {
      _nameController.text = taskType.name;
      _descriptionController.text = taskType.description ?? '';
      _colorController.text = taskType.color ?? '';
      _iconController.text = taskType.icon ?? '';
    } else {
      _resetForm();
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          taskType == null ? 'إنشاء نوع مهمة جديد' : 'تعديل نوع المهمة',
          style: AppStyles.headingMedium,
        ),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم نوع المهمة
                TextFormField(
                  controller: _nameController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'اسم نوع المهمة',
                    hintText: 'أدخل اسم نوع المهمة',
                    prefixIcon: const Icon(Icons.category),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم نوع المهمة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // وصف نوع المهمة
                TextFormField(
                  controller: _descriptionController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'وصف نوع المهمة',
                    hintText: 'أدخل وصف نوع المهمة',
                    prefixIcon: const Icon(Icons.description),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال وصف نوع المهمة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // لون نوع المهمة
                TextFormField(
                  controller: _colorController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'لون نوع المهمة (اختياري)',
                    hintText: 'أدخل قيمة اللون (مثال: #FF5733)',
                    prefixIcon: const Icon(Icons.color_lens),
                    suffixIcon: _colorController.text.isNotEmpty
                        ? Container(
                            margin: const EdgeInsets.all(8),
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: _parseColor(_colorController.text),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.grey),
                            ),
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                const SizedBox(height: 16),
                
                // أيقونة نوع المهمة
                TextFormField(
                  controller: _iconController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'أيقونة نوع المهمة (اختياري)',
                    hintText: 'أدخل اسم الأيقونة',
                    prefixIcon: const Icon(Icons.emoji_objects),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          // زر الإلغاء
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetForm();
            },
            child: const Text('إلغاء'),
          ),
          
          // زر الحفظ
          ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                final name = _nameController.text;
                final description = _descriptionController.text;
                final color = _colorController.text.isEmpty ? null : _colorController.text;
                final icon = _iconController.text.isEmpty ? null : _iconController.text;
                
                bool success;
                
                if (_selectedTaskType == null) {
                  // إنشاء نوع مهمة جديد
                  success = await _taskTypeController.createTaskType(
                    name: name,
                    description: description,
                    color: color,
                    icon: icon,
                  );
                } else {
                  // تحديث نوع المهمة الحالي
                  final updatedTaskType = _selectedTaskType!.copyWith(
                    name: name,
                    description: description,
                    color: color,
                    icon: icon,
                  );
                  
                  success = await _taskTypeController.updateTaskType(updatedTaskType);
                }
                
                if (success) {
                  Get.back();
                  Get.snackbar(
                    _selectedTaskType == null ? 'تم الإنشاء بنجاح' : 'تم التحديث بنجاح',
                    _selectedTaskType == null
                        ? 'تم إنشاء نوع المهمة بنجاح'
                        : 'تم تحديث نوع المهمة بنجاح',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                  );
                  _resetForm();
                } else {
                  Get.snackbar(
                    'خطأ',
                    _taskTypeController.errorMessage.value,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              }
            },
            child: Text(_selectedTaskType == null ? 'إنشاء' : 'تحديث'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(TaskType taskType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف نوع المهمة "${taskType.name}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              final success = await _taskTypeController.deleteTaskType(taskType.id);
              
              if (success) {
                Get.back();
                Get.snackbar(
                  'تم الحذف بنجاح',
                  'تم حذف نوع المهمة بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.back();
                Get.snackbar(
                  'خطأ',
                  _taskTypeController.errorMessage.value,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تحويل قيمة اللون النصية إلى لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return Colors.blue;
    } catch (e) {
      return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_taskTypeController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        if (_taskTypeController.errorMessage.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  _taskTypeController.errorMessage.value,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }
        
        if (_taskTypeController.taskTypes.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.category_outlined,
                  color: Colors.grey,
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد أنواع مهام',
                  style: TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showTaskTypeDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء نوع مهمة جديد'),
                ),
              ],
            ),
          );
        }
        
        return Column(
          children: [
            // شريط الأدوات
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // زر إنشاء نوع مهمة جديد
                  ElevatedButton.icon(
                    onPressed: () => _showTaskTypeDialog(),
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء نوع مهمة جديد'),
                  ),
                  const Spacer(),
                  
                  // خيار عرض الأنواع غير النشطة
                  Row(
                    children: [
                      const Text('عرض الأنواع غير النشطة'),
                      Checkbox(
                        value: _showInactiveTypes,
                        onChanged: (value) {
                          setState(() {
                            _showInactiveTypes = value ?? false;
                            _loadData();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // قائمة أنواع المهام
            Expanded(
              child: ListView.builder(
                itemCount: _taskTypeController.taskTypes.length,
                itemBuilder: (context, index) {
                  final taskType = _taskTypeController.taskTypes[index];
                  
                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: taskType.color != null
                              ? _parseColor(taskType.color!)
                              : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      title: Text(
                        taskType.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: taskType.isActive ? null : Colors.grey,
                        ),
                      ),
                      subtitle: Text(
                        taskType.description ?? 'لا يوجد وصف',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: taskType.isActive ? null : Colors.grey,
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // حالة النشاط
                          if (!taskType.isActive)
                            const Chip(
                              label: Text('غير نشط'),
                              backgroundColor: Colors.grey,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                          
                          // زر التعديل
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _showTaskTypeDialog(taskType: taskType),
                            tooltip: 'تعديل',
                          ),
                          
                          // زر الحذف
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _showDeleteConfirmationDialog(taskType),
                            tooltip: 'حذف',
                            color: Colors.red,
                          ),
                        ],
                      ),
                      onTap: () => _showTaskTypeDialog(taskType: taskType),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}
