import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:get/get.dart';
import '../../controllers/user_permissions_controller.dart';

import '../../controllers/auth_controller.dart';
import '../../models/permission_models.dart';
import '../../models/user_permission_model.dart';
import '../../models/user_model.dart';

/// شاشة عرض صلاحيات المستخدم
class UserPermissionsViewerScreen extends StatefulWidget {
  final int? userId;

  const UserPermissionsViewerScreen({
    super.key,
    this.userId,
  });

  @override
  State<UserPermissionsViewerScreen> createState() =>
      _UserPermissionsViewerScreenState();
}

class _UserPermissionsViewerScreenState
    extends State<UserPermissionsViewerScreen> {
  late final UserPermissionsController _userPermissionsController;
  late final NewPermissionsController _allPermissionsController;
  late final AuthController _authController;
  
  int? _selectedUserId;
  final RxList<UserPermission> _userPermissions = <UserPermission>[].obs;
  final RxList<Permission> _availablePermissions = <Permission>[].obs;
  final RxList<Permission> _deniedPermissions = <Permission>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  @override
  void initState() {
    super.initState();
    _userPermissionsController = Get.find<UserPermissionsController>();
    _allPermissionsController = Get.find<NewPermissionsController>();
    _authController = Get.find<AuthController>();
    _selectedUserId = widget.userId ?? _authController.currentUserId;
    
    if (_selectedUserId != null) {
      _loadUserPermissions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عرض صلاحيات المستخدم'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_authController.hasSystemAdminRights)
            IconButton(
              icon: const Icon(Icons.person_search),
              tooltip: 'اختيار مستخدم آخر',
              onPressed: _showUserSelectionDialog,
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadUserPermissions,
          ),
        ],
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_error.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  'خطأ: ${_error.value}',
                  style: TextStyle(color: Colors.red[700]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadUserPermissions,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (_selectedUserId == null) {
          return const Center(
            child: Text(
              'لم يتم تحديد مستخدم\nانقر على أيقونة البحث لاختيار مستخدم',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          );
        }

        return _buildPermissionsView();
      }),
    );
  }

  /// بناء عرض الصلاحيات
  Widget _buildPermissionsView() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          // معلومات المستخدم
          _buildUserInfoCard(),

          // شريط التبويبات
          Container(
            color: Theme.of(context).primaryColor.withAlpha(25),
            child: const TabBar(
              tabs: [
                Tab(
                  icon: Icon(Icons.check_circle, color: Colors.green),
                  text: 'الصلاحيات الممنوحة',
                ),
                Tab(
                  icon: Icon(Icons.cancel, color: Colors.red),
                  text: 'الصلاحيات المحظورة',
                ),
                Tab(
                  icon: Icon(Icons.list),
                  text: 'جميع الواجهات',
                ),
              ],
            ),
          ),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              children: [
                _buildGrantedPermissionsTab(),
                _buildDeniedPermissionsTab(),
                _buildAllInterfacesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات المستخدم
  Widget _buildUserInfoCard() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Theme.of(context).primaryColor,
              backgroundImage: currentUser.profileImage != null
                  ? NetworkImage(currentUser.profileImage!)
                  : null,
              child: currentUser.profileImage == null
                  ? Text(
                      currentUser.name.isNotEmpty
                          ? currentUser.name[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currentUser.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    currentUser.email,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Chip(
                        label: Text(currentUser.roleName),
                        backgroundColor: _getRoleColor(currentUser.role),
                      ),
                      const SizedBox(width: 8),
                      if (currentUser.departmentId != null)
                        Chip(
                          label: Text(currentUser.departmentId.toString()),
                          backgroundColor: Colors.blue.withAlpha(25),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'إجمالي الصلاحيات الممنوحة',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '${_userPermissions.length}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تبويب الصلاحيات الممنوحة
  Widget _buildGrantedPermissionsTab() {
    if (_userPermissions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.security, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد صلاحيات ممنوحة لهذا المستخدم',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // تجميع الصلاحيات حسب المجموعة
    final Map<String, List<UserPermission>> groupedPermissions = {};
    for (final permission in _userPermissions) {
      final group = permission.permission?.permissionGroup ?? 'غير محدد';
      groupedPermissions.putIfAbsent(group, () => <UserPermission>[]);
      groupedPermissions[group]!.add(permission);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedPermissions.length,
      itemBuilder: (context, index) {
        final group = groupedPermissions.keys.elementAt(index);
        final permissions = groupedPermissions[group]!;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              group,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text('${permissions.length} صلاحية'),
            children: permissions.map((userPermission) {
              return _buildPermissionListItem(
                userPermission.permission!,
                isGranted: true,
                source: _getPermissionSource(userPermission),
                expiresAt: userPermission.expiresAt,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  /// تبويب الصلاحيات المحظورة
  Widget _buildDeniedPermissionsTab() {
    if (_deniedPermissions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'لا توجد صلاحيات محظورة\nيمكن للمستخدم الوصول لجميع الواجهات المسموحة',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _deniedPermissions.length,
      itemBuilder: (context, index) {
        final permission = _deniedPermissions[index];
        return _buildPermissionListItem(
          permission,
          isGranted: false,
          source: 'محظور بواسطة النظام',
        );
      },
    );
  }

  /// تبويب جميع الواجهات
  Widget _buildAllInterfacesTab() {
    // تجميع جميع الواجهات حسب الفئة
    final categorizedScopes = _allPermissionsController.permissionsByGroup;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categorizedScopes.length,
      itemBuilder: (context, index) {
        final category = categorizedScopes.keys.elementAt(index);
        final permissions = categorizedScopes[category]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text('${permissions.length} واجهة'),
            children: permissions.map((permission) {
              final hasAccess = _checkUserAccessToScope(permission);
              return ListTile(
                leading: Icon(
                  hasAccess ? Icons.check_circle : Icons.cancel,
                  color: hasAccess ? Colors.green : Colors.red,
                ),
                title: Text(permission.name),
                subtitle: Text('المجال: ${permission.permissionGroup}'),
                trailing: hasAccess
                    ? const Chip(
                        label: Text('مسموح'),
                        backgroundColor: Colors.green,
                        labelStyle: TextStyle(color: Colors.white),
                      )
                    : const Chip(
                        label: Text('محظور'),
                        backgroundColor: Colors.red,
                        labelStyle: TextStyle(color: Colors.white),
                      ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  /// بناء عنصر الصلاحية في القائمة
  Widget _buildPermissionListItem(
    Permission permission, {
    required bool isGranted,
    String? source,
    int? expiresAt,
  }) {
    return ListTile(
      leading: Icon(
        isGranted ? Icons.check_circle : Icons.cancel,
        color: isGranted ? Colors.green : Colors.red,
      ),
      title: Text(permission.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (permission.description != null)
            Text(permission.description!),
          if (source != null)
            Text(
              'المصدر: $source',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          if (expiresAt != null)
            Text(
              'تنتهي في: ${_formatDate(expiresAt)}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
      trailing: Chip(
        label: Text('المستوى ${permission.level}'),
        backgroundColor: Theme.of(context).primaryColor.withAlpha(25),
      ),
    );
  }

  /// تحميل صلاحيات المستخدم
  Future<void> _loadUserPermissions() async {
    if (_selectedUserId == null) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل صلاحيات المستخدم
      await _userPermissionsController.getUserPermissions(_selectedUserId!);
      _userPermissions.assignAll(_userPermissionsController.userPermissions.cast<UserPermission>());

      // تحميل جميع الصلاحيات المتاحة
      await _allPermissionsController.loadAllPermissions();
      _availablePermissions.assignAll(_allPermissionsController.allPermissions);

      // حساب الصلاحيات المحظورة
      final grantedPermissionIds = _userPermissions
          .map((up) => up.permissionId)
          .toSet();
      
      final deniedPermissions = _availablePermissions
          .where((p) => !grantedPermissionIds.contains(p.id))
          .toList();
      _deniedPermissions.assignAll(deniedPermissions);

    } catch (e) {
      _error.value = e.toString();
    } finally {
      _isLoading.value = false;
    }
  }

  /// عرض حوار اختيار المستخدم
  void _showUserSelectionDialog() {
    // سيتم تنفيذه لاحقاً
    Get.snackbar(
      'قيد التطوير',
      'ميزة اختيار المستخدم قيد التطوير',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// الحصول على لون الدور
  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return Colors.purple.withAlpha(50);
      case UserRole.admin:
        return Colors.red.withAlpha(50);
      case UserRole.manager:
        return Colors.orange.withAlpha(50);
      case UserRole.supervisor:
        return Colors.blue.withAlpha(50);
      case UserRole.user:
        return Colors.green.withAlpha(50);
    }
  }

  /// الحصول على مصدر الصلاحية
  String _getPermissionSource(UserPermission userPermission) {
    if (userPermission.grantedByNavigation != null) {
      return 'منح بواسطة: ${userPermission.grantedByNavigation!.name}';
    }
    return 'صلاحية مباشرة';
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.day}/${date.month}/${date.year}';
  }

  /// التحقق من وصول المستخدم للمجال
  bool _checkUserAccessToScope(Permission permission) {
    // تنفيذ مؤقت - سيتم تطويره لاحقاً
    return _userPermissions.any((up) => up.permissionId == permission.id);
  }
}
