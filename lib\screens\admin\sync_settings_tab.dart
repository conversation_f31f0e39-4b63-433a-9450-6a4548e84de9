// import 'package:flutter/material.dart';
// import 'package:flutter_application_2/constants/app_colors.dart';
// import 'package:get/get.dart';
// import '../../services/sync_service.dart';
// import '../../controllers/auth_controller.dart';
// import '../../constants/app_styles.dart';
// import '../widgets/custom_dropdown.dart';

// /// تبويب إعدادات التزامن
// ///
// /// يوفر واجهة لإدارة إعدادات التزامن في لوحة التحكم الإدارية
// class SyncSettingsTab extends StatefulWidget {
//   const SyncSettingsTab({super.key});

//   @override
//   State<SyncSettingsTab> createState() => _SyncSettingsTabState();
// }

// class _SyncSettingsTabState extends State<SyncSettingsTab> {
//   // خدمة التزامن
//   SyncService? _syncService;

//   @override
//   void initState() {
//     super.initState();
//     _initSyncService();
//   }

//   /// تهيئة خدمة التزامن
//   void _initSyncService() {
//     try {
//       _syncService = Get.find<SyncService>();
//     } catch (e) {
//       // إذا لم تكن خدمة التزامن متاحة، حاول إنشاءها
//       try {
//         final authController = Get.find<AuthController>();
//         if (authController.currentUser.value != null) {
//           _syncService = SyncService(authController.currentUser.value!.id);
//           Get.put(_syncService!, permanent: true);
//         }
//       } catch (e) {
//         debugPrint('خطأ في إنشاء خدمة التزامن: $e');
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _buildHeader(),
//           const SizedBox(height: 16),
//           Expanded(
//             child: _syncService == null
//                 ? _buildNoSyncServiceView()
//                 : _buildSyncSettingsView(),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء رأس الصفحة
//   Widget _buildHeader() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Text(
//           'إعدادات التزامن',
//           style: AppStyles.titleLarge,
//         ),
//         if (_syncService != null)
//           ElevatedButton.icon(
//             onPressed: _restartSyncService,
//             icon: const Icon(Icons.refresh),
//             label: const Text('إعادة تشغيل التزامن'),
//             style: ElevatedButton.styleFrom(
//               backgroundColor: AppColors.accent,
//               foregroundColor: Colors.white,
//             ),
//           ),
//       ],
//     );
//   }

//   /// بناء واجهة عندما تكون خدمة التزامن غير متاحة
//   Widget _buildNoSyncServiceView() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(
//             Icons.sync_problem,
//             size: 64,
//             color: Colors.grey,
//           ),
//           const SizedBox(height: 16),
//           Text(
//             'خدمة التزامن غير متاحة',
//             style: AppStyles.headingMedium,
//           ),
//           const SizedBox(height: 8),
//           Text(
//             'يرجى تسجيل الدخول وإعادة تشغيل التطبيق',
//             style: AppStyles.bodyMedium,
//           ),
//           const SizedBox(height: 24),
//           ElevatedButton(
//             onPressed: _initSyncService,
//             child: const Text('إعادة المحاولة'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء واجهة إعدادات التزامن
//   Widget _buildSyncSettingsView() {
//     // قائمة خيارات الفاصل الزمني للمزامنة
//     final syncIntervalOptions = [
//       DropdownMenuItem(value: 3000, child: const Text('3 ثواني (3000 مللي ثانية)')),
//       DropdownMenuItem(value: 5000, child: const Text('5 ثواني (5000 مللي ثانية) - موصى به')),
//       DropdownMenuItem(value: 10000, child: const Text('10 ثواني (10000 مللي ثانية)')),
//       DropdownMenuItem(value: 15000, child: const Text('15 ثانية (15000 مللي ثانية)')),
//       DropdownMenuItem(value: 30000, child: const Text('30 ثانية (30000 مللي ثانية)')),
//       DropdownMenuItem(value: 60000, child: const Text('دقيقة واحدة (60000 مللي ثانية)')),
//     ];

//     return SingleChildScrollView(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // معلومات التزامن
//           Card(
//             elevation: 2,
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'حالة التزامن',
//                     style: AppStyles.headingMedium,
//                   ),
//                   const SizedBox(height: 16),
//                   Obx(() => _buildSyncStatusRow(
//                         'حالة الاتصال:',
//                         _syncService!.isSyncing.value
//                             ? 'جاري التزامن'
//                             : 'متصل',
//                         _syncService!.isSyncing.value
//                             ? Colors.blue
//                             : Colors.green,
//                       )),
//                   const SizedBox(height: 8),
//                   Obx(() => _buildSyncStatusRow(
//                         'آخر تحديث:',
//                         _syncService!.syncStatus.value.isEmpty
//                             ? 'لا يوجد تحديث'
//                             : _syncService!.syncStatus.value,
//                         Colors.black,
//                       )),
//                 ],
//               ),
//             ),
//           ),
//           const SizedBox(height: 24),

//           // إعدادات التزامن
//           Card(
//             elevation: 2,
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'إعدادات التزامن',
//                     style: AppStyles.headingMedium,
//                   ),
//                   const SizedBox(height: 16),

//                   // الفاصل الزمني للمزامنة
//                   Obx(() => CustomDropdown<int>(
//                     label: 'الفاصل الزمني للمزامنة',
//                     value: _syncService!.syncIntervalMs.value,
//                     items: syncIntervalOptions,
//                     onChanged: (value) {
//                       if (value != null) {
//                         _syncService!.syncIntervalMs.value = value;
//                         _syncService!.saveSyncSettings();
//                       }
//                     },
//                     hint: 'اختر الفاصل الزمني للمزامنة',
//                   )),
//                   const SizedBox(height: 16),

//                   // إعادة المحاولة التلقائية
//                   Obx(() => SwitchListTile(
//                     title: const Text('إعادة المحاولة التلقائية'),
//                     subtitle: const Text('إعادة المحاولة تلقائيًا عند فشل المزامنة'),
//                     value: _syncService!.autoRetry.value,
//                     onChanged: (value) {
//                       _syncService!.autoRetry.value = value;
//                       _syncService!.saveSyncSettings();
//                     },
//                     activeColor: AppColors.primary,
//                   )),

//                   // ملاحظة حول إزالة الأقفال
//                   const SizedBox(height: 16),
//                   Container(
//                     padding: const EdgeInsets.all(12),
//                     decoration: BoxDecoration(
//                       color: Colors.blue.shade50,
//                       borderRadius: BorderRadius.circular(8),
//                       border: Border.all(color: Colors.blue.shade200),
//                     ),
//                     child: Row(
//                       children: [
//                         Icon(Icons.info_outline, color: Colors.blue.shade700),
//                         const SizedBox(width: 12),
//                         Expanded(
//                           child: Text(
//                             'تم إزالة نظام الأقفال من التطبيق لتحسين الأداء وتجنب مشكلات الانتظار.',
//                             style: TextStyle(color: Colors.blue.shade700),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           const SizedBox(height: 24),

//           // معلومات عن التزامن
//           Card(
//             elevation: 2,
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'معلومات عن التزامن',
//                     style: AppStyles.headingMedium,
//                   ),
//                   const SizedBox(height: 16),
//                   Text(
//                     'يتيح نظام التزامن للمستخدمين العمل معًا في نفس الوقت على نفس البيانات. '
//                     'يتم تزامن التغييرات تلقائيًا بين جميع المستخدمين المتصلين بنفس قاعدة البيانات.',
//                     style: AppStyles.bodyMedium,
//                   ),
//                   const SizedBox(height: 16),
//                   Text(
//                     'البيانات التي يتم تزامنها:',
//                     style: AppStyles.bodyMedium.copyWith(
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   _buildSyncFeatureRow('الرسائل والمحادثات'),
//                   _buildSyncFeatureRow('المهام وحالاتها'),
//                   _buildSyncFeatureRow('التعليقات والمرفقات'),
//                   _buildSyncFeatureRow('الإشعارات'),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء صف لعرض حالة التزامن
//   Widget _buildSyncStatusRow(String label, String value, Color valueColor) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Text(
//           label,
//           style: AppStyles.bodyMedium,
//         ),
//         Text(
//           value,
//           style: AppStyles.bodyMedium.copyWith(
//             color: valueColor,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ],
//     );
//   }

//   /// بناء صف لعرض ميزة التزامن
//   Widget _buildSyncFeatureRow(String feature) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8.0),
//       child: Row(
//         children: [
//           const Icon(
//             Icons.check_circle,
//             color: AppColors.success,
//             size: 20,
//           ),
//           const SizedBox(width: 8),
//           Text(
//             feature,
//             style: AppStyles.bodyMedium,
//           ),
//         ],
//       ),
//     );
//   }

//   /// إعادة تشغيل خدمة التزامن
//   void _restartSyncService() {
//     if (_syncService != null) {
//       Get.delete<SyncService>();
//       final authController = Get.find<AuthController>();
//       if (authController.currentUser.value != null) {
//         final newSyncService = SyncService(authController.currentUser.value!.id);
//         Get.put(newSyncService, permanent: true);
//         setState(() {
//           _syncService = newSyncService;
//         });
//         Get.snackbar(
//           'إعادة تشغيل التزامن',
//           'تم إعادة تشغيل خدمة التزامن بنجاح',
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: Colors.green.shade100,
//           colorText: Colors.green.shade800,
//         );
//       }
//     }
//   }
// }
