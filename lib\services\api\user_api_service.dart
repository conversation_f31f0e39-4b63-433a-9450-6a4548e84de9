import 'package:flutter/foundation.dart';
import '../../models/user_model.dart';
import 'api_service.dart';

/// خدمة API للمستخدمين
class UserApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiService.get('/api/Users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');

      // في حالة خطأ المصادقة، نحاول إعادة تسجيل الدخول
      if (e is ApiException && e.statusCode == 401) {
        debugPrint('خطأ في المصادقة، محاولة إعادة تسجيل الدخول...');
        // يمكن إضافة منطق إعادة تسجيل الدخول هنا
      }

      rethrow;
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم: $e');
      return null;
    }
  }

  /// إنشاء مستخدم جديد
  Future<User?> createUser(User user) async {
    try {
      final response = await _apiService.post(
        '/api/Users',
        user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث مستخدم
  Future<User?> updateUser(User user) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>',
        user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم: $e');
      rethrow;
    }
  }

  /// حذف مستخدم (حذف ناعم)
  Future<bool> deleteUser(int id) async {
    try {
      final response = await _apiService.delete('/api/Users/<USER>');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين النشطين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين المتصلين
  Future<List<User>> getOnlineUsers() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين المتصلين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين بدور معين
  Future<List<User>> getUsersByRole(int roleId) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/$roleId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين بالدور: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين في قسم معين
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/$departmentId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي القسم: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين في أقسام متعددة
  Future<List<User>> getUsersByDepartmentIds(List<int> departmentIds) async {
    try {
      if (departmentIds.isEmpty) return [];
      
      final response = await _apiService.get(
        '/api/Users/<USER>',
        queryParams: {'departmentIds': departmentIds.join(',')},
      );
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي الأقسام: $e');
      rethrow;
    }
  }

  /// البحث عن المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدمين: $e');
      rethrow;
    }
  }

  /// تحديث حالة النشاط للمستخدم
  Future<bool> updateUserActiveStatus(int userId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/active',
        {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة النشاط: $e');
      return false;
    }
  }

  /// تحديث حالة الاتصال للمستخدم
  Future<bool> updateUserOnlineStatus(int userId, bool isOnline) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/online',
        {'isOnline': isOnline},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الاتصال: $e');
      return false;
    }
  }

  /// تحديث دور المستخدم
  Future<bool> updateUserRole(int userId, int roleId) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/role',
        {'roleId': roleId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }

  /// تحديث قسم المستخدم
  Future<bool> updateUserDepartment(int userId, int? departmentId) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/department',
        {'departmentId': departmentId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث قسم المستخدم: $e');
      return false;
    }
  }

  // تم نقل طرق الأقسام إلى department_api_service.dart
}
