import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart' as excel_lib;
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:async';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../models/database_table_model.dart';
import '../../utils/responsive_helper.dart';
import 'database_row_editor.dart';

/// واجهة عرض جدول البيانات باستخدام PlutoGrid
///
/// توفر واجهة متقدمة لعرض وتحرير بيانات جدول مع ميزات PlutoGrid
class DatabaseTableView extends StatefulWidget {
  final DatabaseManagementController controller;
  final DatabaseTable table;

  const DatabaseTableView({
    super.key,
    required this.controller,
    required this.table,
  });

  @override
  State<DatabaseTableView> createState() => _DatabaseTableViewState();
}

class _DatabaseTableViewState extends State<DatabaseTableView> {
  final TextEditingController _searchController = TextEditingController();

  // PlutoGrid state manager
  PlutoGridStateManager? _plutoGridStateManager;

  // Debouncing للبحث
  Timer? _searchTimer;

  @override
  void initState() {
    super.initState();

    // تحميل بيانات الجدول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller.loadTableData();
    });
  }

  /// إنشاء أعمدة PlutoGrid
  List<PlutoColumn> _createPlutoColumns() {
    final columns = <PlutoColumn>[];

    // عمود ترقيم الصفوف
    columns.add(
      PlutoColumn(
        title: '#',
        field: '_row_number',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 60,
        minWidth: 60,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          final rowIndex = rendererContext.stateManager.refRows
                  .indexOf(rendererContext.row) +
              1;
          return Container(
            alignment: Alignment.center,
            child: Text(
              '$rowIndex',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          );
        },
      ),
    );

    // عمود سحب الصفوف
    columns.add(
      PlutoColumn(
        title: '↕',
        field: '_row_drag',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        enableRowDrag: true, // تفعيل سحب الصفوف
        width: 40,
        minWidth: 40,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          return const Icon(
            Icons.drag_handle,
            color: Colors.grey,
            size: 18,
          );
        },
      ),
    );

    // عمود الإجراءات
    columns.add(
      PlutoColumn(
        title: 'الإجراءات',
        field: '_actions',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 120,
        minWidth: 120,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          return _buildActionsCell(rendererContext.row.toJson());
        },
      ),
    );

    // أعمدة البيانات
    final visibleColumns =
        widget.table.columns.where((col) => col.isVisibleInList).toList();

    for (final col in visibleColumns) {
      columns.add(_createPlutoColumnFromDatabaseColumn(col));
    }

    return columns;
  }

  /// إنشاء عمود PlutoGrid من عمود قاعدة البيانات
  PlutoColumn _createPlutoColumnFromDatabaseColumn(DatabaseColumn col) {
    PlutoColumnType columnType;

    // تحديد نوع العمود
    switch (col.type) {
      case DatabaseColumnType.integer:
      case DatabaseColumnType.decimal:
      case DatabaseColumnType.real:
        columnType = PlutoColumnType.number();
        break;
      case DatabaseColumnType.date:
        columnType = PlutoColumnType.date();
        break;
      case DatabaseColumnType.datetime:
      case DatabaseColumnType.time:
        columnType = PlutoColumnType.time();
        break;
      case DatabaseColumnType.boolean:
        columnType = PlutoColumnType.select(['true', 'false']);
        break;
      case DatabaseColumnType.enumType:
        if (col.allowedValueNames != null &&
            col.allowedValueNames!.isNotEmpty) {
          columnType = PlutoColumnType.select(col.allowedValueNames!);
        } else {
          columnType = PlutoColumnType.text();
        }
        break;
      default:
        columnType = PlutoColumnType.text();
    }

    return PlutoColumn(
      title: col.displayName ?? col.name,
      field: col.name,
      type: columnType,
      enableEditingMode: col.isEditable && widget.table.isEditable,
      enableSorting: col.isSortable,
      enableColumnDrag: true, // تفعيل سحب الأعمدة
      enableContextMenu: true, // تفعيل قائمة السياق
      enableFilterMenuItem: true, // تفعيل التصفية
      enableAutoEditing: false, // منع التحرير التلقائي
      width: _getColumnWidth(col),
      minWidth: 80,
      renderer: (rendererContext) {
        return _buildPlutoCellContent(col, rendererContext.cell.value);
      },
    );
  }

  /// تحديد عرض العمود
  double _getColumnWidth(DatabaseColumn col) {
    switch (col.type) {
      case DatabaseColumnType.boolean:
        return 100;
      case DatabaseColumnType.date:
        return 120;
      case DatabaseColumnType.datetime:
      case DatabaseColumnType.time:
        return 160;
      case DatabaseColumnType.integer:
        return 100;
      case DatabaseColumnType.decimal:
      case DatabaseColumnType.real:
        return 120;
      case DatabaseColumnType.color:
        return 120;
      case DatabaseColumnType.image:
      case DatabaseColumnType.file:
        return 100;
      default:
        return 150;
    }
  }

  /// بناء محتوى خلية PlutoGrid
  Widget _buildPlutoCellContent(DatabaseColumn column, dynamic value) {
    if (value == null) {
      return const Text('-');
    }

    switch (column.type) {
      case DatabaseColumnType.boolean:
        return Icon(
          value == 1 ||
                  value == true ||
                  value.toString().toLowerCase() == 'true'
              ? Icons.check_circle
              : Icons.cancel,
          color: value == 1 ||
                  value == true ||
                  value.toString().toLowerCase() == 'true'
              ? Colors.green
              : Colors.red,
          size: 20,
        );

      case DatabaseColumnType.color:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: _parseColor(value.toString()),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(value.toString()),
          ],
        );

      case DatabaseColumnType.image:
        return value.toString().isNotEmpty
            ? const Icon(Icons.image, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.file:
        return value.toString().isNotEmpty
            ? const Icon(Icons.insert_drive_file, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.password:
        return const Text('••••••••');

      default:
        return Text(
          value.toString(),
          overflow: TextOverflow.ellipsis,
        );
    }
  }

  /// بناء خلية الإجراءات
  Widget _buildActionsCell(Map<String, dynamic> row) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.table.isEditable)
          IconButton(
            icon: const Icon(Icons.edit, size: 18),
            tooltip: 'تعديل',
            onPressed: () => _showEditRowDialog(row),
          ),
        if (widget.table.isDeletable)
          IconButton(
            icon: const Icon(Icons.delete, size: 18),
            tooltip: 'حذف',
            color: AppColors.error,
            onPressed: () => _showDeleteConfirmation(row),
          ),
      ],
    );
  }

  /// إنشاء صفوف PlutoGrid من بيانات الجدول
  /// تم تعطيل هذه الدالة لصالح Lazy Pagination
  /*
  List<PlutoRow> _createPlutoRows() {
    final rows = <PlutoRow>[];

    for (final rowData in widget.controller.tableData) {
      final cells = <String, PlutoCell>{};

      // خلية ترقيم الصفوف
      cells['_row_number'] = PlutoCell(value: 0); // سيتم تحديثها في renderer

      // خلية سحب الصفوف
      cells['_row_drag'] = PlutoCell(value: '');

      // خلية الإجراءات
      cells['_actions'] = PlutoCell(value: '');

      // خلايا البيانات
      final visibleColumns = widget.table.columns
          .where((col) => col.isVisibleInList)
          .toList();

      for (final col in visibleColumns) {
        final value = rowData[col.name];
        cells[col.name] = PlutoCell(value: value ?? '');
      }

      rows.add(PlutoRow(cells: cells));
    }

    return rows;
  }
  */

  @override
  void didUpdateWidget(DatabaseTableView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إذا تغير الجدول، مسح البحث السابق
    if (oldWidget.table.name != widget.table.name) {
      _searchController.clear(); // مسح نص البحث السابق
      widget.controller.clearSearch(); // مسح البحث من الكنترولر
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  /// تحديث بيانات الجدول مع إعادة تحميل PlutoGrid
  void _refreshGridData() {
    // إعادة تحميل البيانات
    widget.controller.loadTableData().then((_) {
      // إذا كان PlutoGrid موجود، قم بتحديثه
      if (_plutoGridStateManager != null) {
        // إعادة تحميل البيانات في PlutoGrid
        _plutoGridStateManager!.refRows.clear();

        // إضافة البيانات الجديدة
        final newRows = _createPlutoRowsFromData(widget.controller.tableData);
        _plutoGridStateManager!.refRows.addAll(newRows);

        // تحديث العرض
        _plutoGridStateManager!.notifyListeners();

        debugPrint('تم تحديث PlutoGrid مع ${newRows.length} صف');
      }
    });
  }

  /// إنشاء صفوف PlutoGrid من البيانات
  List<PlutoRow> _createPlutoRowsFromData(List<Map<String, dynamic>> data) {
    final rows = <PlutoRow>[];

    for (int i = 0; i < data.length; i++) {
      final rowData = data[i];
      final cells = <String, PlutoCell>{};

      // خلية ترقيم الصفوف
      cells['_row_number'] = PlutoCell(value: i + 1);

      // خلية سحب الصفوف
      cells['_row_drag'] = PlutoCell(value: '');

      // خلية الإجراءات
      cells['_actions'] = PlutoCell(value: '');

      // خلايا البيانات
      final visibleColumns =
          widget.table.columns.where((col) => col.isVisibleInList).toList();

      for (final col in visibleColumns) {
        dynamic value = rowData[col.name];

        // معالجة القيم الخاصة
        if (value == null) {
          value = '';
        } else if (col.type == DatabaseColumnType.boolean) {
          if (value is bool) {
            value = value;
          } else if (value is int) {
            value = value == 1;
          } else if (value is String) {
            value = value.toLowerCase() == 'true' || value == '1';
          }
        } else if (col.type == DatabaseColumnType.datetime && value is String) {
          try {
            final dateTime = DateTime.parse(value);
            value = dateTime.toLocal().toString().split('.')[0];
          } catch (e) {
            // الاحتفاظ بالقيمة الأصلية إذا فشل التحويل
          }
        }

        cells[col.name] = PlutoCell(value: value);
      }

      rows.add(PlutoRow(cells: cells));
    }

    return rows;
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isTablet(context) ||
        ResponsiveHelper.isDesktop(context);

    return Obx(() {
      // عرض التخطيط الكامل مع شريط الأدوات في جميع الحالات
      return Column(
        children: [
          // شريط الأدوات - يظهر دائماً
          _buildToolbar(isLargeScreen),

          // عرض البيانات
          Expanded(
            child: _buildDataView(),
          ),
        ],
      );
    });
  }

  /// بناء عرض البيانات
  Widget _buildDataView() {
    if (widget.controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (widget.controller.error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: AppColors.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.controller.error,
              textAlign: TextAlign.center,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                widget.controller.loadTableData();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // إذا كانت البيانات فارغة، عرض PlutoGrid فارغ مع رسالة
    if (widget.controller.tableData.isEmpty) {
      return _buildEmptyPlutoGrid();
    }

    // عرض PlutoGrid مع البيانات
    return _buildPlutoGrid();
  }

  Widget _buildToolbar(bool isLargeScreen) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                // عنوان الجدول مع إحصائيات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            widget.table.displayName,
                            style: AppStyles.headingMedium,
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: widget.controller.tableData.isEmpty
                                  ? Colors.grey.shade200
                                  : AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${widget.controller.tableData.length} سجل',
                              style: AppStyles.bodySmall.copyWith(
                                color: widget.controller.tableData.isEmpty
                                    ? Colors.grey.shade600
                                    : AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.table.description ?? 'لا يوجد وصف',
                        style: AppStyles.bodyMedium.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // أزرار الإجراءات
                if (widget.table.isCreatable)
                  ElevatedButton.icon(
                    onPressed: _showAddRowDialog,
                    icon: const Icon(Icons.add),
                    label: Text(isLargeScreen ? 'إضافة سجل جديد' : 'إضافة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                const SizedBox(width: 8),
                if (widget.table.isExportable)
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (widget.controller.tableData.isEmpty) {
                        Get.snackbar(
                          'تنبيه',
                          'لا توجد بيانات للتصدير',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.orange,
                          colorText: Colors.white,
                        );
                        return;
                      }

                      switch (value) {
                        case 'csv':
                          _exportToCsv();
                          break;
                        case 'excel':
                          _exportToExcel();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'csv',
                        child: Row(
                          children: [
                            Icon(Icons.table_chart),
                            SizedBox(width: 8),
                            Text('تصدير CSV'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'excel',
                        child: Row(
                          children: [
                            Icon(Icons.grid_on),
                            SizedBox(width: 8),
                            Text('تصدير Excel'),
                          ],
                        ),
                      ),
                    ],
                    child: OutlinedButton.icon(
                      onPressed:
                          null, // سيتم التعامل مع الضغط عبر PopupMenuButton
                      icon: const Icon(Icons.download),
                      label: Text(isLargeScreen ? 'تصدير البيانات' : 'تصدير'),
                    ),
                  ),
                const SizedBox(width: 8),
                if (widget.table.isImportable)
                  OutlinedButton.icon(
                    onPressed: _importFromCsv,
                    icon: const Icon(Icons.upload),
                    label: Text(isLargeScreen ? 'استيراد البيانات' : 'استيراد'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // حقل البحث العام
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث عام في جميع الحقول...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                widget.controller.clearSearch();
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onSubmitted: (value) {
                      debugPrint('تم إرسال البحث: "$value"');
                      if (value.isNotEmpty) {
                        debugPrint(
                            'تنفيذ البحث في الجدول: ${widget.table.name}');
                        widget.controller.search(value);
                      } else {
                        debugPrint('مسح البحث');
                        widget.controller.clearSearch();
                      }
                    },
                    onChanged: (value) {
                      // البحث التلقائي أثناء الكتابة (اختياري)
                      if (value.isEmpty) {
                        widget.controller.clearSearch();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // زر البحث
                ElevatedButton(
                  onPressed: () {
                    final searchText = _searchController.text;
                    debugPrint('تم الضغط على زر البحث: "$searchText"');
                    if (searchText.isNotEmpty) {
                      debugPrint(
                          'تنفيذ البحث العام في الجدول: ${widget.table.name}');
                      widget.controller.search(searchText);
                    } else {
                      debugPrint('مسح البحث من زر البحث');
                      widget.controller.clearSearch();
                    }
                  },
                  child: const Text('بحث'),
                ),
                const SizedBox(width: 8),

                // زر إزالة التصفية
                if (widget.controller.filterClause.value.isNotEmpty ||
                    widget.controller.searchText.value.isNotEmpty)
                  OutlinedButton.icon(
                    onPressed: () {
                      _searchController.clear();
                      widget.controller.clearFilter();
                      widget.controller.clearSearch();
                    },
                    icon: const Icon(Icons.filter_alt_off),
                    label: const Text('إزالة التصفية'),
                  ),

                // زر تحديث البيانات
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    widget.controller.loadTableData();
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث البيانات',
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء PlutoGrid فارغ مع رسالة
  Widget _buildEmptyPlutoGrid() {
    // إنشاء الأعمدة فقط (بدون صفوف)
    final columns = _createPlutoColumns();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              border: Border(
                bottom:
                    BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  '${widget.table.displayName} - 0 سجل',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // عرض أسماء الأعمدة
          if (columns.isNotEmpty) ...[
            Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Row(
                children: columns.map((column) {
                  return Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      alignment: Alignment.centerRight,
                      child: Text(
                        column.title,
                        style: AppStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],

          // رسالة الجدول الفارغ
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.table_rows_outlined,
                        color: Colors.grey.shade400,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'لا توجد بيانات في هذا الجدول',
                        style: AppStyles.titleMedium.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'استخدم شريط الأدوات أعلاه لإضافة البيانات أو استيرادها',
                        textAlign: TextAlign.center,
                        style: AppStyles.bodyMedium.copyWith(
                          color: Colors.grey.shade500,
                        ),
                      ),
                      if (widget.table.isCreatable) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _showAddRowDialog,
                          icon: const Icon(Icons.add, size: 18),
                          label: const Text('إضافة سجل جديد'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء PlutoGrid مع Lazy Pagination
  Widget _buildPlutoGrid() {
    // إنشاء الأعمدة
    final columns = _createPlutoColumns();

    // بدء بقائمة فارغة - سيتم تحميل البيانات عبر lazy pagination
    final rows = <PlutoRow>[];

    return PlutoGrid(
      columns: columns,
      rows: rows,
      onLoaded: (PlutoGridOnLoadedEvent event) {
        _plutoGridStateManager = event.stateManager;

        // تطبيق إعدادات PlutoGrid المتقدمة
        _plutoGridStateManager?.setConfiguration(
          PlutoGridConfiguration(
            style: const PlutoGridStyleConfig(
              gridBorderRadius: BorderRadius.all(Radius.circular(8)),
              enableGridBorderShadow: true,
              enableColumnBorderVertical: true,
              enableColumnBorderHorizontal: true,
              enableCellBorderVertical: true,
              enableCellBorderHorizontal: true,
              enableRowColorAnimation: true,
              gridBorderColor: Colors.grey,
              activatedBorderColor: Colors.blue,
              inactivatedBorderColor: Colors.grey,
              borderColor: Colors.grey,
              rowHeight: 45,
              columnHeight: 45,
              columnFilterHeight: 35,
              cellTextStyle: TextStyle(fontSize: 14),
              columnTextStyle:
                  TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              iconSize: 18,
              defaultColumnTitlePadding: EdgeInsets.symmetric(horizontal: 8),
              defaultCellPadding:
                  EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
            columnSize: const PlutoGridColumnSizeConfig(
              autoSizeMode: PlutoAutoSizeMode.scale,
              resizeMode: PlutoResizeMode.pushAndPull,
              restoreAutoSizeAfterHideColumn: true,
              restoreAutoSizeAfterFrozenColumn: true,
              restoreAutoSizeAfterMoveColumn: true,
              restoreAutoSizeAfterInsertColumn: true,
              restoreAutoSizeAfterRemoveColumn: true,
            ),
            scrollbar: const PlutoGridScrollbarConfig(
              draggableScrollbar: true,
              isAlwaysShown: true,
              scrollbarThickness: 8,
              scrollbarThicknessWhileDragging: 10,
              scrollbarRadius: Radius.circular(4),
              scrollbarRadiusWhileDragging: Radius.circular(6),
            ),
            columnFilter: PlutoGridColumnFilterConfig(
              filters: const [
                PlutoFilterTypeContains(),
                PlutoFilterTypeEquals(),
                PlutoFilterTypeStartsWith(),
                PlutoFilterTypeEndsWith(),
                PlutoFilterTypeGreaterThan(),
                PlutoFilterTypeGreaterThanOrEqualTo(),
                PlutoFilterTypeLessThan(),
                PlutoFilterTypeLessThanOrEqualTo(),
              ],
            ),
            enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
            enableMoveDownAfterSelecting: true,
            enableMoveHorizontalInEditing: true,
            localeText: PlutoGridLocaleText.arabic(),
          ),
        );

        // تفعيل التصفية
        _plutoGridStateManager?.setShowColumnFilter(true);
      },
      onChanged: (PlutoGridOnChangedEvent event) {
        // التعامل مع تغيير البيانات
        debugPrint('تم تغيير الخلية: ${event.column.field} = ${event.value}');
      },
      onSorted: (PlutoGridOnSortedEvent event) {
        // التعامل مع الترتيب
        final columnName = event.column.field;
        final isAscending = event.column.sort == PlutoColumnSort.ascending;
        widget.controller.changeOrder(columnName, isAscending);
      },
      onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
        // فتح نافذة التحرير عند النقر المزدوج
        if (widget.table.isEditable) {
          final rowData = event.row.toJson();
          _showEditRowDialog(rowData);
        }
      },
      onRowsMoved: (PlutoGridOnRowsMovedEvent event) {
        // التعامل مع سحب الصفوف
        debugPrint('تم نقل الصفوف: ${event.idx} صف');
        // يمكن إضافة منطق لحفظ ترتيب الصفوف الجديد
      },
      createHeader: (stateManager) {
        return Obx(() {
          return Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom:
                    BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  '${widget.table.displayName} - ${widget.controller.tableData.length} سجل',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        });
      },
      // إضافة Lazy Pagination المحسن
      createFooter: (stateManager) {
        return PlutoLazyPagination(
          initialPage: 1,
          initialFetch: true,
          fetchWithSorting: true,
          fetchWithFiltering: true,
          pageSizeToMove: 3, // تقليل عدد الصفحات للتحميل السريع
          fetch: _fetchDataForPagination,
          stateManager: stateManager,
        );
      },
      // تحسين الأداء
      mode: PlutoGridMode.normal, // وضع عادي للأداء الأفضل
      configuration: PlutoGridConfiguration(
        // إعدادات التمرير المحسنة
        scrollbar: const PlutoGridScrollbarConfig(
          draggableScrollbar: true,
          isAlwaysShown: true,
          scrollbarThickness: 8,
          scrollbarThicknessWhileDragging: 10,
          scrollbarRadius: Radius.circular(4),
          scrollbarRadiusWhileDragging: Radius.circular(6),
        ),
        // إعدادات الأعمدة المحسنة
        columnSize: const PlutoGridColumnSizeConfig(
          autoSizeMode: PlutoAutoSizeMode.scale,
          resizeMode: PlutoResizeMode.pushAndPull,
          restoreAutoSizeAfterHideColumn: true,
          restoreAutoSizeAfterFrozenColumn: true,
          restoreAutoSizeAfterMoveColumn: true,
          restoreAutoSizeAfterInsertColumn: true,
          restoreAutoSizeAfterRemoveColumn: true,
        ),
        // إعدادات التصفية المحسنة
        columnFilter: PlutoGridColumnFilterConfig(
          filters: const [
            PlutoFilterTypeContains(),
            PlutoFilterTypeEquals(),
            PlutoFilterTypeStartsWith(),
            PlutoFilterTypeEndsWith(),
            PlutoFilterTypeGreaterThan(),
            PlutoFilterTypeGreaterThanOrEqualTo(),
            PlutoFilterTypeLessThan(),
            PlutoFilterTypeLessThanOrEqualTo(),
          ],
          debounceMilliseconds: 300, // تأخير للبحث السريع
        ),
        // إعدادات التفاعل
        enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
        enableMoveDownAfterSelecting: true,
        enableMoveHorizontalInEditing: true,
        // النصوص العربية
        localeText: PlutoGridLocaleText.arabic(),
        // إعدادات الأداء
        style: const PlutoGridStyleConfig(
          enableGridBorderShadow: false, // تعطيل الظلال لتحسين الأداء
          enableRowColorAnimation: false, // تعطيل الرسوم المتحركة لتحسين الأداء
          gridBorderRadius: BorderRadius.all(Radius.circular(8)),
          enableColumnBorderVertical: true,
          enableColumnBorderHorizontal: true,
          enableCellBorderVertical: true,
          enableCellBorderHorizontal: true,
          gridBorderColor: Colors.grey,
          activatedBorderColor: Colors.blue,
          inactivatedBorderColor: Colors.grey,
          borderColor: Colors.grey,
          rowHeight: 45,
          columnHeight: 45,
          columnFilterHeight: 35,
          cellTextStyle: TextStyle(fontSize: 14),
          columnTextStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          iconSize: 18,
          defaultColumnTitlePadding: EdgeInsets.symmetric(horizontal: 8),
          defaultCellPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
      ),
    );
  }

  /// دالة تحميل البيانات للـ Lazy Pagination المحسنة
  Future<PlutoLazyPaginationResponse> _fetchDataForPagination(
    PlutoLazyPaginationRequest request,
  ) async {
    try {
      debugPrint('=== بدء تحميل البيانات للـ Lazy Pagination ===');
      debugPrint('الصفحة المطلوبة: ${request.page}');

      // تحديث معاملات الكنترولر باستخدام الطرق العامة
      widget.controller.goToPage(request.page);

      // معالجة الفلاتر المتقدمة
      Map<String, dynamic>? filters;
      if (request.filterRows.isNotEmpty) {
        filters = {};
        debugPrint('معالجة ${request.filterRows.length} فلتر');

        for (final filterRow in request.filterRows) {
          for (final cell in filterRow.cells.entries) {
            final columnField = cell.key;
            final cellValue = cell.value.value;

            // تجاهل الأعمدة الخاصة
            if (columnField.startsWith('_')) continue;

            if (cellValue != null && cellValue.toString().trim().isNotEmpty) {
              filters[columnField] = cellValue.toString().trim();
              debugPrint('فلتر: $columnField = ${cellValue.toString()}');
            }
          }
        }
      }

      // معالجة الترتيب المتقدم
      String? orderBy;
      String orderDirection = 'ASC';
      if (request.sortColumn != null && !request.sortColumn!.sort.isNone) {
        orderBy = request.sortColumn!.field;
        orderDirection = request.sortColumn!.sort == PlutoColumnSort.ascending
            ? 'ASC'
            : 'DESC';

        // تجاهل الأعمدة الخاصة
        if (orderBy.startsWith('_')) {
          orderBy = null;
        } else {
          debugPrint('ترتيب: $orderBy $orderDirection');
          widget.controller.changeOrder(orderBy, orderDirection == 'ASC');
        }
      }

      // تطبيق الفلاتر إذا وجدت
      if (filters != null && filters.isNotEmpty) {
        widget.controller.applyFilters(filters);
      }

      // تحميل البيانات مع معلومات Pagination
      final result = await widget.controller.loadTableDataWithPagination();

      final data = result['data'] as List<Map<String, dynamic>>;
      final totalRecords = result['totalRecords'] as int;
      final totalPages = result['totalPages'] as int;

      debugPrint('تم تحميل ${data.length} سجل من أصل $totalRecords سجل');
      debugPrint('إجمالي الصفحات: $totalPages');

      // تحويل البيانات إلى PlutoRows مع تحسينات
      final rows = <PlutoRow>[];

      for (int i = 0; i < data.length; i++) {
        final rowData = data[i];
        final cells = <String, PlutoCell>{};

        // خلية ترقيم الصفوف (رقم عالمي)
        final globalRowNumber =
            ((request.page - 1) * widget.controller.pageSize) + i + 1;
        cells['_row_number'] = PlutoCell(value: globalRowNumber);

        // خلية سحب الصفوف
        cells['_row_drag'] = PlutoCell(value: '');

        // خلية الإجراءات
        cells['_actions'] = PlutoCell(value: '');

        // خلايا البيانات مع معالجة محسنة
        final visibleColumns =
            widget.table.columns.where((col) => col.isVisibleInList).toList();

        for (final col in visibleColumns) {
          dynamic value = rowData[col.name];

          // معالجة القيم الخاصة
          if (value == null) {
            value = '';
          } else if (col.type == DatabaseColumnType.boolean) {
            // تحويل القيم المنطقية
            if (value is bool) {
              value = value;
            } else if (value is int) {
              value = value == 1;
            } else if (value is String) {
              value = value.toLowerCase() == 'true' || value == '1';
            }
          } else if (col.type == DatabaseColumnType.datetime &&
              value is String) {
            // معالجة التواريخ
            try {
              final dateTime = DateTime.parse(value);
              value = dateTime
                  .toLocal()
                  .toString()
                  .split('.')[0]; // إزالة الميلي ثانية
            } catch (e) {
              // الاحتفاظ بالقيمة الأصلية إذا فشل التحويل
            }
          }

          cells[col.name] = PlutoCell(value: value);
        }

        rows.add(PlutoRow(cells: cells));
      }

      debugPrint('تم إنشاء ${rows.length} صف للعرض');
      debugPrint('=== انتهاء تحميل البيانات للـ Lazy Pagination ===');

      return PlutoLazyPaginationResponse(
        totalPage: totalPages > 0 ? totalPages : 1,
        rows: rows,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات للـ pagination: $e');

      // إرجاع استجابة فارغة في حالة الخطأ
      return PlutoLazyPaginationResponse(
        totalPage: 1,
        rows: [],
      );
    }
  }

  void _showAddRowDialog() {
    // تنفيذ إضافة سجل جديد
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة سجل جديد'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              onSave: (row) {
                Navigator.of(context).pop();
                widget.controller.createRecord(row).then((success) {
                  if (success) {
                    // مسح cache المفاتيح الخارجية للجدول المحدث
                    widget.controller
                        .clearSpecificForeignKeyCache(widget.table.name);

                    // إعادة تحميل البيانات مع تحديث PlutoGrid
                    _refreshGridData();

                    // عرض رسالة نجاح
                    Get.snackbar(
                      'تم بنجاح',
                      'تم إضافة السجل الجديد بنجاح',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 3),
                    );
                  }
                });
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showEditRowDialog(Map<String, dynamic> row) {
    // تنفيذ تعديل سجل
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل سجل'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              initialValues: row,
              onSave: (updatedRow) {
                Navigator.of(context).pop();

                // البحث عن العمود المفتاح الأساسي
                final primaryKeyColumn = widget.table.columns.firstWhere(
                  (col) => col.isPrimaryKey,
                  orElse: () => widget.table.columns.first,
                );

                widget.controller
                    .updateRow(updatedRow, primaryKeyColumn.name)
                    .then((success) {
                  if (success) {
                    // مسح cache المفاتيح الخارجية للجدول المحدث
                    widget.controller
                        .clearSpecificForeignKeyCache(widget.table.name);

                    // إعادة تحميل البيانات مع تحديث PlutoGrid
                    _refreshGridData();

                    // عرض رسالة نجاح
                    Get.snackbar(
                      'تم بنجاح',
                      'تم تحديث السجل بنجاح',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 3),
                    );
                  }
                });
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> row) {
    // البحث عن العمود المفتاح الأساسي
    final primaryKeyColumn = widget.table.columns.firstWhere(
      (col) => col.isPrimaryKey,
      orElse: () => widget.table.columns.first,
    );

    final primaryKeyValue = row[primaryKeyColumn.name];

    // البحث عن عمود العرض
    final displayColumn = widget.table.columns.firstWhere(
      (col) => col.isVisibleInList && !col.isPrimaryKey,
      orElse: () => primaryKeyColumn,
    );

    final displayValue = row[displayColumn.name];

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف السجل "${displayValue ?? primaryKeyValue}"؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.controller.deleteRecord(primaryKeyValue).then((success) {
                  if (success) {
                    // مسح cache المفاتيح الخارجية للجدول المحدث
                    widget.controller
                        .clearSpecificForeignKeyCache(widget.table.name);

                    // إعادة تحميل البيانات مع تحديث PlutoGrid
                    _refreshGridData();

                    // عرض رسالة نجاح
                    Get.snackbar(
                      'تم بنجاح',
                      'تم حذف السجل بنجاح',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 3),
                    );
                  }
                });
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// تصدير البيانات إلى CSV مع ميزات متقدمة
  void _exportToCsv() async {
    try {
      // عرض خيارات التصدير
      final exportOptions = await _showExportOptionsDialog();
      if (exportOptions == null) return;

      // عرض مؤشر التحميل مع تقدم
      Get.dialog(
        PopScope(
          canPop: false,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  const Text('جاري تصدير البيانات...'),
                  const SizedBox(height: 8),
                  Obx(() => Text(
                        'تم تصدير ${widget.controller.tableData.length} سجل',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      )),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // تحضير البيانات للتصدير
      final List<List<String>> csvData = [];

      // إضافة معلومات الجدول كتعليق (اختياري)
      if (exportOptions['includeMetadata'] == true) {
        csvData.add(['# تصدير بيانات الجدول: ${widget.table.displayName}']);
        csvData.add(['# تاريخ التصدير: ${DateTime.now().toString()}']);
        csvData.add(['# عدد السجلات: ${widget.controller.tableData.length}']);
        csvData.add([]); // سطر فارغ
      }

      // إضافة رؤوس الأعمدة
      final headers = <String>[];
      final visibleColumns =
          widget.table.columns.where((col) => col.isVisibleInList).toList();

      for (final column in visibleColumns) {
        String header = column.displayName ?? column.name;
        if (exportOptions['includeColumnTypes'] == true) {
          header += ' (${column.type.name})';
        }
        headers.add(header);
      }
      csvData.add(headers);

      // إضافة بيانات الصفوف مع معالجة محسنة
      for (final rowData in widget.controller.tableData) {
        final row = <String>[];
        for (final column in visibleColumns) {
          final value = rowData[column.name];
          String cellValue = '';

          if (value != null) {
            switch (column.type) {
              case DatabaseColumnType.boolean:
                cellValue = (value == true ||
                        value == 1 ||
                        value.toString().toLowerCase() == 'true')
                    ? 'نعم'
                    : 'لا';
                break;
              case DatabaseColumnType.datetime:
                if (value is String && value.isNotEmpty) {
                  try {
                    final dateTime = DateTime.parse(value);
                    cellValue =
                        '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
                  } catch (e) {
                    cellValue = value.toString();
                  }
                } else {
                  cellValue = value.toString();
                }
                break;
              case DatabaseColumnType.decimal:
              case DatabaseColumnType.real:
                if (value is num) {
                  cellValue = value.toStringAsFixed(2);
                } else {
                  cellValue = value.toString();
                }
                break;
              default:
                cellValue = value.toString();
            }
          }

          row.add(cellValue);
        }
        csvData.add(row);
      }

      // تحويل البيانات إلى CSV مع إعدادات محسنة
      final csvString = const ListToCsvConverter(
        fieldDelimiter: ',',
        textDelimiter: '"',
        textEndDelimiter: '"',
        eol: '\n',
      ).convert(csvData);

      // إضافة BOM للدعم الأفضل للغة العربية
      final bom = [0xEF, 0xBB, 0xBF];
      final csvBytes = utf8.encode(csvString);
      final bytes = Uint8List.fromList([...bom, ...csvBytes]);

      // إنشاء اسم ملف ذكي
      final timestamp = DateTime.now();
      final formattedDate =
          '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')}';
      final fileName = '${widget.table.displayName}_$formattedDate.csv';

      // حفظ الملف
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: bytes,
        ext: 'csv',
        mimeType: MimeType.csv,
      );

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح مفصلة
      Get.snackbar(
        'تم التصدير بنجاح',
        'تم تصدير ${widget.controller.tableData.length} سجل من ${widget.table.displayName} إلى ملف CSV',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
        mainButton: TextButton(
          onPressed: () => Get.back(),
          child: const Text('موافق', style: TextStyle(color: Colors.white)),
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
      debugPrint('خطأ في تصدير CSV: $e');
    }
  }

  /// تصدير البيانات إلى Excel مع تنسيق متقدم
  void _exportToExcel() async {
    try {
      // عرض خيارات التصدير
      final exportOptions = await _showExportOptionsDialog();
      if (exportOptions == null) return;

      // عرض مؤشر التحميل مع تقدم
      Get.dialog(
        PopScope(
          canPop: false,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  const Text('جاري تصدير البيانات إلى Excel...'),
                  const SizedBox(height: 8),
                  Obx(() => Text(
                        'تم تصدير ${widget.controller.tableData.length} سجل',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      )),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // إنشاء ملف Excel جديد
      final excel_lib.Excel excel = excel_lib.Excel.createExcel();
      final sheet = excel['البيانات']; // اسم الورقة بالعربية

      // حذف الورقة الافتراضية
      excel.delete('Sheet1');

      int currentRow = 0;

      // إضافة معلومات الجدول (اختياري)
      if (exportOptions['includeMetadata'] == true) {
        // عنوان الجدول
        final titleCell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: 0, rowIndex: currentRow));
        titleCell.value =
            excel_lib.TextCellValue('تقرير: ${widget.table.displayName}');
        titleCell.cellStyle = excel_lib.CellStyle(
          bold: true,
          fontSize: 16,
          backgroundColorHex: excel_lib.ExcelColor.blue,
          fontColorHex: excel_lib.ExcelColor.white,
        );
        currentRow++;

        // تاريخ التصدير
        final dateCell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: 0, rowIndex: currentRow));
        dateCell.value = excel_lib.TextCellValue(
            'تاريخ التصدير: ${DateTime.now().toString().split('.')[0]}');
        dateCell.cellStyle = excel_lib.CellStyle(italic: true);
        currentRow++;

        // عدد السجلات
        final countCell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: 0, rowIndex: currentRow));
        countCell.value = excel_lib.TextCellValue(
            'عدد السجلات: ${widget.controller.tableData.length}');
        countCell.cellStyle = excel_lib.CellStyle(italic: true);
        currentRow += 2; // سطر فارغ
      }

      // إضافة رؤوس الأعمدة مع تنسيق محسن
      final visibleColumns =
          widget.table.columns.where((col) => col.isVisibleInList).toList();
      for (int i = 0; i < visibleColumns.length; i++) {
        final column = visibleColumns[i];
        final cell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: currentRow));

        String headerText = column.displayName ?? column.name;
        if (exportOptions['includeColumnTypes'] == true) {
          headerText += ' (${column.type.name})';
        }

        cell.value = excel_lib.TextCellValue(headerText);
        cell.cellStyle = excel_lib.CellStyle(
          bold: true,
          backgroundColorHex: excel_lib.ExcelColor.lightBlue,
          fontColorHex: excel_lib.ExcelColor.black,
          horizontalAlign: excel_lib.HorizontalAlign.Center,
          verticalAlign: excel_lib.VerticalAlign.Center,
        );
      }
      currentRow++;

      // إضافة بيانات الصفوف مع تنسيق محسن
      for (int rowIndex = 0;
          rowIndex < widget.controller.tableData.length;
          rowIndex++) {
        final rowData = widget.controller.tableData[rowIndex];

        for (int colIndex = 0; colIndex < visibleColumns.length; colIndex++) {
          final column = visibleColumns[colIndex];
          final cell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
              columnIndex: colIndex, rowIndex: currentRow + rowIndex));

          final value = rowData[column.name];

          if (value != null) {
            // معالجة محسنة للقيم حسب النوع
            switch (column.type) {
              case DatabaseColumnType.integer:
                if (value is num) {
                  cell.value = excel_lib.IntCellValue(value.toInt());
                } else {
                  final intValue = int.tryParse(value.toString());
                  cell.value = intValue != null
                      ? excel_lib.IntCellValue(intValue)
                      : excel_lib.TextCellValue(value.toString());
                }
                break;
              case DatabaseColumnType.decimal:
              case DatabaseColumnType.real:
                if (value is num) {
                  cell.value = excel_lib.DoubleCellValue(value.toDouble());
                } else {
                  final doubleValue = double.tryParse(value.toString());
                  cell.value = doubleValue != null
                      ? excel_lib.DoubleCellValue(doubleValue)
                      : excel_lib.TextCellValue(value.toString());
                }
                break;
              case DatabaseColumnType.boolean:
                bool boolValue = false;
                if (value is bool) {
                  boolValue = value;
                } else if (value is int) {
                  boolValue = value == 1;
                } else if (value is String) {
                  boolValue = value.toLowerCase() == 'true' || value == '1';
                }
                cell.value = excel_lib.TextCellValue(boolValue ? 'نعم' : 'لا');
                cell.cellStyle = excel_lib.CellStyle(
                  backgroundColorHex: boolValue
                      ? excel_lib.ExcelColor.green
                      : excel_lib.ExcelColor.none,
                );
                break;
              case DatabaseColumnType.datetime:
                if (value is String && value.isNotEmpty) {
                  try {
                    final dateTime = DateTime.parse(value);
                    cell.value = excel_lib.TextCellValue(
                        '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}');
                  } catch (e) {
                    cell.value = excel_lib.TextCellValue(value.toString());
                  }
                } else {
                  cell.value = excel_lib.TextCellValue(value.toString());
                }
                break;
              default:
                cell.value = excel_lib.TextCellValue(value.toString());
            }
          } else {
            cell.value = excel_lib.TextCellValue('');
          }

          // تنسيق الصفوف المتناوبة
          if (rowIndex % 2 == 1) {
            cell.cellStyle = excel_lib.CellStyle(
              backgroundColorHex: excel_lib.ExcelColor.none,
            );
          }
        }
      }

      // تطبيق عرض تلقائي للأعمدة
      for (int i = 0; i < visibleColumns.length; i++) {
        sheet.setColumnAutoFit(i);
      }

      // حفظ الملف
      final bytes = excel.encode();
      if (bytes != null) {
        final timestamp = DateTime.now();
        final formattedDate =
            '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')}';
        final fileName = '${widget.table.displayName}_$formattedDate.xlsx';

        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: Uint8List.fromList(bytes),
          ext: 'xlsx',
          mimeType: MimeType.microsoftExcel,
        );
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح مفصلة
      Get.snackbar(
        'تم التصدير بنجاح',
        'تم تصدير ${widget.controller.tableData.length} سجل من ${widget.table.displayName} إلى ملف Excel',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
        mainButton: TextButton(
          onPressed: () => Get.back(),
          child: const Text('موافق', style: TextStyle(color: Colors.white)),
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات إلى Excel: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
      debugPrint('خطأ في تصدير Excel: $e');
    }
  }

  /// استيراد البيانات من ملف CSV مع ميزات متقدمة
  void _importFromCsv() async {
    try {
      // اختيار ملف CSV
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'txt'],
        allowMultiple: false,
        dialogTitle: 'اختر ملف CSV للاستيراد',
      );

      if (result == null || result.files.isEmpty) {
        return; // المستخدم ألغى العملية
      }

      final file = result.files.first;
      if (file.bytes == null) {
        Get.snackbar(
          'خطأ',
          'لا يمكن قراءة الملف المحدد',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // التحقق من حجم الملف
      final fileSizeKB = file.size / 1024;
      if (fileSizeKB > 5120) {
        // 5MB
        final confirmed = await Get.dialog<bool>(
          AlertDialog(
            title: const Text('ملف كبير'),
            content: Text(
              'حجم الملف ${fileSizeKB.toStringAsFixed(1)} KB. قد يستغرق الاستيراد وقتاً أطول. هل تريد المتابعة؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: const Text('متابعة'),
              ),
            ],
          ),
        );
        if (confirmed != true) return;
      }

      // عرض مربع حوار لتأكيد الاستيراد مع خيارات
      final importOptions = await _showImportOptionsDialog(file.name);
      if (importOptions == null) return;

      // عرض مؤشر التحميل مع تقدم
      Get.dialog(
        PopScope(
          canPop: false,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  const Text('جاري استيراد البيانات...'),
                  const SizedBox(height: 8),
                  const Text(
                    'يرجى الانتظار...',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // قراءة وتحليل ملف CSV مع معالجة محسنة
      String csvString;
      try {
        // محاولة قراءة الملف بـ UTF-8 أولاً
        csvString = utf8.decode(file.bytes!);
      } catch (e) {
        // إذا فشل، محاولة قراءة بـ Latin-1
        try {
          csvString = latin1.decode(file.bytes!);
        } catch (e2) {
          Get.back(); // إغلاق مؤشر التحميل
          Get.snackbar(
            'خطأ في الترميز',
            'لا يمكن قراءة الملف. تأكد من أن الملف بترميز UTF-8 أو Latin-1',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }
      }

      final csvData = const CsvToListConverter(
        fieldDelimiter: ',',
        textDelimiter: '"',
        eol: '\n',
      ).convert(csvString);

      if (csvData.isEmpty) {
        Get.back(); // إغلاق مؤشر التحميل
        Get.snackbar(
          'خطأ',
          'الملف فارغ أو لا يحتوي على بيانات صالحة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // معالجة الرؤوس والبيانات حسب الخيارات
      List<String> headers;
      List<List<dynamic>> dataRows;

      if (importOptions['skipFirstRow'] == true && csvData.isNotEmpty) {
        headers = csvData.first.map((e) => e.toString().trim()).toList();
        dataRows = csvData.skip(1).toList();
      } else {
        // إنشاء رؤوس افتراضية
        final firstRow = csvData.isNotEmpty ? csvData.first : [];
        headers =
            List.generate(firstRow.length, (index) => 'Column${index + 1}');
        dataRows = csvData;
      }

      if (dataRows.isEmpty) {
        Get.back(); // إغلاق مؤشر التحميل
        Get.snackbar(
          'تحذير',
          'لا توجد بيانات للاستيراد في الملف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      // تحويل البيانات إلى تنسيق مناسب للإرسال إلى API مع validation
      final importedRecords = <Map<String, dynamic>>[];
      final validationErrors = <String>[];
      final validateData = importOptions['validateData'] == true;

      for (int rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
        final row = dataRows[rowIndex];
        final record = <String, dynamic>{};
        bool hasValidationError = false;

        for (int i = 0; i < headers.length && i < row.length; i++) {
          final columnName = headers[i].trim();
          final value = row[i];

          // تجاهل الأعمدة الفارغة
          if (columnName.isEmpty) continue;

          // البحث عن العمود المقابل في الجدول
          DatabaseColumn? column;
          try {
            column = widget.table.columns.firstWhere(
              (col) =>
                  col.displayName?.toLowerCase() == columnName.toLowerCase() ||
                  col.name.toLowerCase() == columnName.toLowerCase(),
            );
          } catch (e) {
            // إذا لم يتم العثور على العمود، إنشاء عمود افتراضي
            column =
                DatabaseColumn(name: columnName, type: DatabaseColumnType.text);
          }

          // تحويل القيمة حسب نوع العمود مع validation
          dynamic convertedValue = value;
          String? validationError;

          if (value != null && value.toString().trim().isNotEmpty) {
            final stringValue = value.toString().trim();

            switch (column.type) {
              case DatabaseColumnType.integer:
                final intValue = int.tryParse(stringValue);
                if (intValue != null) {
                  convertedValue = intValue;
                } else if (validateData) {
                  validationError =
                      'الصف ${rowIndex + 1}, العمود "$columnName": القيمة "$stringValue" ليست رقماً صحيحاً';
                  hasValidationError = true;
                } else {
                  convertedValue = 0; // قيمة افتراضية
                }
                break;
              case DatabaseColumnType.decimal:
              case DatabaseColumnType.real:
                final doubleValue = double.tryParse(stringValue);
                if (doubleValue != null) {
                  convertedValue = doubleValue;
                } else if (validateData) {
                  validationError =
                      'الصف ${rowIndex + 1}, العمود "$columnName": القيمة "$stringValue" ليست رقماً عشرياً';
                  hasValidationError = true;
                } else {
                  convertedValue = 0.0; // قيمة افتراضية
                }
                break;
              case DatabaseColumnType.boolean:
                final lowerValue = stringValue.toLowerCase();
                if (['true', '1', 'نعم', 'yes', 'صحيح'].contains(lowerValue)) {
                  convertedValue = true;
                } else if (['false', '0', 'لا', 'no', 'خطأ']
                    .contains(lowerValue)) {
                  convertedValue = false;
                } else if (validateData) {
                  validationError =
                      'الصف ${rowIndex + 1}, العمود "$columnName": القيمة "$stringValue" ليست قيمة منطقية صحيحة';
                  hasValidationError = true;
                } else {
                  convertedValue = false; // قيمة افتراضية
                }
                break;
              case DatabaseColumnType.datetime:
                try {
                  // محاولة تحليل التاريخ بتنسيقات مختلفة
                  DateTime? dateTime;
                  final dateFormats = [
                    stringValue, // التنسيق الأصلي
                    stringValue.replaceAll('/', '-'), // تحويل / إلى -
                    stringValue.replaceAll('-', '/'), // تحويل - إلى /
                  ];

                  for (final format in dateFormats) {
                    try {
                      dateTime = DateTime.parse(format);
                      break;
                    } catch (e) {
                      // تجربة التنسيق التالي
                    }
                  }

                  if (dateTime != null) {
                    convertedValue = dateTime.toIso8601String();
                  } else if (validateData) {
                    validationError =
                        'الصف ${rowIndex + 1}, العمود "$columnName": القيمة "$stringValue" ليست تاريخاً صحيحاً';
                    hasValidationError = true;
                  } else {
                    convertedValue = stringValue; // الاحتفاظ بالقيمة الأصلية
                  }
                } catch (e) {
                  if (validateData) {
                    validationError =
                        'الصف ${rowIndex + 1}, العمود "$columnName": خطأ في تحليل التاريخ "$stringValue"';
                    hasValidationError = true;
                  } else {
                    convertedValue = stringValue;
                  }
                }
                break;
              default:
                convertedValue = stringValue;
            }

            if (validationError != null) {
              validationErrors.add(validationError);
            }
          } else {
            // قيمة فارغة
            if (column.isRequired && validateData) {
              validationError =
                  'الصف ${rowIndex + 1}, العمود "$columnName": هذا الحقل مطلوب';
              hasValidationError = true;
              validationErrors.add(validationError);
            } else {
              convertedValue = null;
            }
          }

          // تجاهل الأعمدة التي تزيد تلقائياً
          if (!column.isAutoIncrement) {
            record[column.name] = convertedValue;
          }
        }

        // إضافة السجل إذا لم يكن هناك أخطاء أو إذا كان المستخدم لا يريد التوقف عند الأخطاء
        if (!hasValidationError || importOptions['stopOnError'] != true) {
          if (record.isNotEmpty) {
            importedRecords.add(record);
          }
        } else if (importOptions['stopOnError'] == true) {
          break; // التوقف عند أول خطأ
        }
      }

      // عرض أخطاء التحقق إذا وجدت
      if (validationErrors.isNotEmpty && validateData) {
        Get.back(); // إغلاق مؤشر التحميل

        final showErrors = await Get.dialog<bool>(
          AlertDialog(
            title:
                Text('تم العثور على ${validationErrors.length} خطأ في التحقق'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: validationErrors.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '${index + 1}. ${validationErrors[index]}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('إلغاء الاستيراد'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: const Text('متابعة مع الأخطاء'),
              ),
            ],
          ),
        );

        if (showErrors != true) return;

        // إعادة عرض مؤشر التحميل
        Get.dialog(
          PopScope(
            canPop: false,
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري استيراد البيانات...'),
                  ],
                ),
              ),
            ),
          ),
          barrierDismissible: false,
        );
      }

      // استيراد السجلات مع تقدم محسن
      int successCount = 0;
      int errorCount = 0;
      final importErrors = <String>[];

      for (int i = 0; i < importedRecords.length; i++) {
        final record = importedRecords[i];

        try {
          final success = await widget.controller.createRecord(record);
          if (success) {
            successCount++;
          } else {
            errorCount++;
            importErrors.add('السجل ${i + 1}: فشل في الإنشاء');
          }
        } catch (e) {
          errorCount++;
          importErrors.add('السجل ${i + 1}: $e');
          debugPrint('خطأ في استيراد السجل ${i + 1}: $e');

          // التوقف عند الخطأ إذا كان المستخدم يريد ذلك
          if (importOptions['stopOnError'] == true) {
            break;
          }
        }

        // تحديث مؤشر التقدم كل 10 سجلات
        if (i % 10 == 0) {
          debugPrint('تم استيراد ${i + 1} من ${importedRecords.length} سجل');
        }
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض نتائج الاستيراد المفصلة
      final totalRecords = importedRecords.length;
      final hasErrors = errorCount > 0;
      final hasValidationErrors = validationErrors.isNotEmpty;

      String resultTitle = 'نتائج الاستيراد';
      String resultMessage =
          'تم استيراد $successCount من أصل $totalRecords سجل بنجاح';

      if (hasErrors) {
        resultMessage += '\nفشل في استيراد $errorCount سجل';
      }

      if (hasValidationErrors) {
        resultMessage +=
            '\nتم العثور على ${validationErrors.length} تحذير في التحقق';
      }

      Color backgroundColor = Colors.green;
      if (errorCount > successCount) {
        backgroundColor = Colors.red;
      } else if (hasErrors || hasValidationErrors) {
        backgroundColor = Colors.orange;
      }

      Get.snackbar(
        resultTitle,
        resultMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: backgroundColor,
        colorText: Colors.white,
        duration: const Duration(seconds: 6),
        mainButton: hasErrors
            ? TextButton(
                onPressed: () {
                  // عرض تفاصيل الأخطاء
                  Get.dialog(
                    AlertDialog(
                      title: Text('تفاصيل الأخطاء ($errorCount خطأ)'),
                      content: SizedBox(
                        width: double.maxFinite,
                        height: 300,
                        child: ListView.builder(
                          itemCount: importErrors.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                '${index + 1}. ${importErrors[index]}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            );
                          },
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Get.back(),
                          child: const Text('موافق'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('عرض الأخطاء',
                    style: TextStyle(color: Colors.white)),
              )
            : null,
      );

      // إعادة تحميل البيانات إذا تم استيراد أي سجل بنجاح
      if (successCount > 0) {
        await widget.controller.loadTableData();
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في الاستيراد',
        'حدث خطأ أثناء استيراد البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض مربع حوار خيارات التصدير
  Future<Map<String, dynamic>?> _showExportOptionsDialog() async {
    bool includeMetadata = true;
    bool includeColumnTypes = false;

    return await Get.dialog<Map<String, dynamic>>(
      AlertDialog(
        title: const Text('خيارات التصدير'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CheckboxListTile(
                  title: const Text('تضمين معلومات الجدول'),
                  subtitle: const Text('تاريخ التصدير وعدد السجلات'),
                  value: includeMetadata,
                  onChanged: (value) {
                    setState(() {
                      includeMetadata = value ?? true;
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text('تضمين أنواع الأعمدة'),
                  subtitle: const Text('إضافة نوع البيانات لكل عمود'),
                  value: includeColumnTypes,
                  onChanged: (value) {
                    setState(() {
                      includeColumnTypes = value ?? false;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: {
              'includeMetadata': includeMetadata,
              'includeColumnTypes': includeColumnTypes,
            }),
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار خيارات الاستيراد
  Future<Map<String, dynamic>?> _showImportOptionsDialog(
      String fileName) async {
    bool skipFirstRow = true;
    bool validateData = true;
    bool stopOnError = false;

    return await Get.dialog<Map<String, dynamic>>(
      AlertDialog(
        title: const Text('خيارات الاستيراد'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الملف: $fileName',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('تجاهل الصف الأول'),
                  subtitle: const Text('إذا كان يحتوي على رؤوس الأعمدة'),
                  value: skipFirstRow,
                  onChanged: (value) {
                    setState(() {
                      skipFirstRow = value ?? true;
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text('التحقق من صحة البيانات'),
                  subtitle: const Text('فحص أنواع البيانات قبل الاستيراد'),
                  value: validateData,
                  onChanged: (value) {
                    setState(() {
                      validateData = value ?? true;
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text('التوقف عند الخطأ'),
                  subtitle: const Text('إيقاف الاستيراد عند أول خطأ'),
                  value: stopOnError,
                  onChanged: (value) {
                    setState(() {
                      stopOnError = value ?? false;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: {
              'skipFirstRow': skipFirstRow,
              'validateData': validateData,
              'stopOnError': stopOnError,
            }),
            child: const Text('استيراد'),
          ),
        ],
      ),
    );
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return Colors.blue;
    } catch (e) {
      return Colors.grey;
    }
  }
}
