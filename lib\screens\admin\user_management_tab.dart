import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:io';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../models/user_model.dart';
import '../../utils/file_processor.dart';
import '../../utils/image_helper.dart';

import '../../services/api/image_upload_api_service.dart';

/// تبويب إدارة المستخدمين
///
/// يوفر واجهة لإدارة المستخدمين وصلاحياتهم
class UserManagementTab extends StatefulWidget {
  final void Function(User user)? onGoToUserPermissions;
  const UserManagementTab({super.key, this.onGoToUserPermissions});

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  final AdminController _adminController = Get.find<AdminController>();
  final ImageUploadApiService _imageUploadService = ImageUploadApiService();
  final TextEditingController _searchController = TextEditingController();
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxBool _isSearching = false.obs;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isInitialized = false;

  // حالة التحميل المحلية لتجنب التداخل مع حالة التحميل العامة
  final RxBool _isLocalLoading = false.obs;

  // متغيرات PlutoGrid
  PlutoGridStateManager? _plutoGridStateManager;

  // متغيرات التجميع
  final RxBool _isGroupingEnabled = false.obs;
  final RxList<PlutoColumn> _groupedColumns = <PlutoColumn>[].obs;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // تأجيل جميع العمليات إلى ما بعد اكتمال البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupTabListener();
      _initializeData();
    });
  }

  /// إعداد مراقب التبويب
  void _setupTabListener() {
    try {
      final tabController = Get.find<TabController>(tag: 'admin_tab_controller');
      tabController.addListener(() {
        // التحقق من أن التبويب الحالي هو تبويب إدارة المستخدمين (الفهرس 0)
        if (tabController.index == 0 && mounted) {
          debugPrint('تم الانتقال إلى تبويب إدارة المستخدمين');
          // تأجيل العملية لتجنب التداخل مع عملية البناء
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _checkAndRefreshData();
            }
          });
        }
      });
    } catch (e) {
      debugPrint('خطأ في إعداد مراقب التبويب: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && mounted) {
      // تأجيل إعادة تحميل البيانات عند العودة للتطبيق
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _checkAndRefreshData();
        }
      });
    }
  }

  /// التحقق من البيانات وإعادة تحميلها إذا لزم الأمر
  Future<void> _checkAndRefreshData() async {
    try {
      // التحقق من حالة التحميل المحلية لتجنب التحميل المتكرر
      if (_isLocalLoading.value) {
        debugPrint('البيانات قيد التحميل بالفعل، تم تجاهل الطلب');
        return;
      }

      // إذا كانت قائمة المستخدمين فارغة، قم بتحميلها
      if (_adminController.users.isEmpty) {
        debugPrint('قائمة المستخدمين فارغة، سيتم تحميل البيانات');
        await _initializeData();
      } else {
        // إذا كانت البيانات موجودة، قم بتحديث القائمة المفلترة فقط
        debugPrint('تحديث القائمة المفلترة مع ${_adminController.users.length} مستخدم');
        _filteredUsers.assignAll(_adminController.users);
        _filterUsers();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من البيانات: $e');
    }
  }

  /// تهيئة البيانات
  Future<void> _initializeData() async {
    if (_isInitialized && _adminController.users.isNotEmpty) {
      _filteredUsers.assignAll(_adminController.users);
      return;
    }

    _isLocalLoading.value = true;
    try {
      // تحميل المستخدمين دائماً للتأكد من الحصول على أحدث البيانات
      await _adminController.loadAllUsers();
      _filteredUsers.assignAll(_adminController.users);
      _isInitialized = true;
      debugPrint('تم تحميل ${_adminController.users.length} مستخدم في UserManagementTab');
    } catch (e) {
      debugPrint('خطأ في تهيئة بيانات المستخدمين: $e');
      // تأجيل عرض الرسالة لتجنب التداخل مع عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Get.snackbar(
            'خطأ في التحميل',
            'حدث خطأ أثناء تحميل بيانات المستخدمين',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      });
    } finally {
      _isLocalLoading.value = false;
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    _isLocalLoading.value = true;
    try {
      await _adminController.loadAllUsers();
      _filteredUsers.assignAll(_adminController.users);
      _filterUsers(); // إعادة تطبيق المرشحات

      Get.snackbar(
        'تم التحديث',
        'تم تحديث بيانات المستخدمين بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المستخدمين: $e');
      Get.snackbar(
        'خطأ في التحديث',
        'فشل في تحديث بيانات المستخدمين',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isLocalLoading.value = false;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  /// فلترة المستخدمين بناءً على نص البحث (مستخلص لإعادة الاستخدام)
  List<User> _filterUsersList(List<User> users, String searchText) {
    final lowerSearch = searchText.toLowerCase();
    if (lowerSearch.isEmpty) return users;
    return users.where((user) =>
      user.name.toLowerCase().contains(lowerSearch) ||
      user.email.toLowerCase().contains(lowerSearch)
    ).toList();
  }

  /// تصفية المستخدمين بناءً على نص البحث
  void _filterUsers() {
    final searchText = _searchController.text;
    _filteredUsers.assignAll(_filterUsersList(_adminController.users, searchText));
    // تحديث PlutoGrid إذا كان موجوداً
    if (_plutoGridStateManager != null) {
      final newRows = _createPlutoRows();
      _plutoGridStateManager!.removeAllRows();
      _plutoGridStateManager!.appendRows(newRows);
    }
  }

  /// حوار موحد لإضافة أو تعديل مستخدم
  void _showUserDialog({User? user}) {
    final isEdit = user != null;
    final nameController = TextEditingController(text: user?.name ?? '');
    final emailController = TextEditingController(text: user?.email ?? '');
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    // 1. تصحيح متغير selectedRole ليكون من النوع الصحيح
    UserRole selectedRole = user?.role is UserRole
        ? user?.role as UserRole
        : UserRole.user;
    File? selectedImage;

    Get.dialog(
      AlertDialog(
        title: Text(isEdit ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // صورة المستخدم
                  Center(
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          backgroundImage: selectedImage != null
                              ? FileImage(selectedImage!)
                              : (user?.profileImage != null
                                  ? NetworkImage(user!.profileImage!)
                                  : null) as ImageProvider?,
                          child: (selectedImage == null && user?.profileImage == null)
                              ? const Icon(Icons.person, size: 40)
                              : null,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.camera_alt, size: 20),
                            onPressed: () async {
                              await _showImagePickerOptions(context, (file) {
                                setState(() {
                                  selectedImage = file;
                                });
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // حقول البيانات
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      hintText: 'أدخل اسم المستخدم',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: 'أدخل البريد الإلكتروني',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  if (!isEdit) ...[
                    const SizedBox(height: 16),
                    TextField(
                      controller: passwordController,
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        hintText: 'أدخل كلمة المرور',
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: confirmPasswordController,
                      decoration: const InputDecoration(
                        labelText: 'تأكيد كلمة المرور',
                        hintText: 'أدخل كلمة المرور مرة أخرى',
                      ),
                      obscureText: true,
                    ),
                  ],
                  const SizedBox(height: 16),
                  DropdownButtonFormField<UserRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                    ),
                    items: UserRole.values.map((role) {
                      return DropdownMenuItem<UserRole>(
                        value: role,
                        child: Text(_getRoleText(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.isEmpty || emailController.text.isEmpty || (!isEdit && passwordController.text.isEmpty)) {
                Get.snackbar(
                  'خطأ',
                  'يرجى ملء جميع الحقول المطلوبة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              if (!isEdit && passwordController.text != confirmPasswordController.text) {
                Get.snackbar(
                  'خطأ',
                  'كلمة المرور وتأكيدها غير متطابقين',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              String? profileImagePath = user?.profileImage;
              // رفع الصورة إذا تم اختيارها
              if (selectedImage != null) {
                try {
                  // 2. تصحيح استدعاء uploadProfileImage ليأخذ معاملين (الصورة وID المستخدم)
                  profileImagePath = await _imageUploadService.uploadProfileImage(selectedImage!, isEdit ? user!.id : 0);
                } catch (e) {
                  debugPrint('خطأ في رفع صورة الملف الشخصي: $e');
                }
              }
              if (isEdit) {
                // تحديث المستخدم
                final updatedUser = user.copyWith(
                  name: nameController.text.trim(),
                  email: emailController.text.trim(),
                  profileImage: profileImagePath,
                  roleId: selectedRole.value, // استخدام roleId بدلاً من role
                );
                final result = await _adminController.updateUser(updatedUser);
                if (result) {
                  await _refreshData();
                  Get.back();
                  Get.snackbar('تم التحديث', 'تم تحديث المستخدم بنجاح', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green, colorText: Colors.white);
                } else {
                  Get.snackbar('خطأ', 'فشل تحديث المستخدم', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red, colorText: Colors.white);
                }
              } else {
                // إضافة مستخدم جديد
                final newUser = User(
                  id: 0,
                  name: nameController.text.trim(),
                  email: emailController.text.trim(),
                  password: passwordController.text,
                  profileImage: profileImagePath,
                  roleId: selectedRole.value, // استخدام roleId بدلاً من role
                  isActive: true,
                  createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                );
                final success = await _adminController.createUser(newUser);
                if (success) {
                  await _refreshData();
                  Get.back();
                  Get.snackbar('تم بنجاح', 'تم إضافة المستخدم بنجاح', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green, colorText: Colors.white);
                } else {
                  Get.snackbar('خطأ', 'فشل إضافة المستخدم', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red, colorText: Colors.white);
                }
              }
            },
            child: Text(isEdit ? 'حفظ' : 'إضافة'),
          ),
        ],
      ),
    );
  }
  /// إنشاء أعمدة PlutoGrid مع دعم التعديل المباشر للدور
  List<PlutoColumn> _createPlutoColumns() {
    return [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.number(),
        hide: true,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'الاسم',
        field: 'name',
        type: PlutoColumnType.text(),
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: 'البريد الإلكتروني',
        field: 'email',
        type: PlutoColumnType.text(),
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: 'الدور',
        field: 'role',
        // جعل الدور قابل للاختيار من قائمة
        type: PlutoColumnType.select([
          'مستخدم',
          'مشرف',
          'مدير',
          'مدير عام',
          'مدير النظام',
        ]),
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: 'الحالة',
        field: 'status',
        type: PlutoColumnType.select(['نشط', 'معطل']),
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: 'تاريخ الإنشاء',
        field: 'created_at',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'الإجراءات',
        field: 'actions',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
      ),
    ];
  }

  /// إنشاء صفوف PlutoGrid
  List<PlutoRow> _createPlutoRows() {
    return _filteredUsers.map((user) {
      return PlutoRow(cells: {
        'id': PlutoCell(value: user.id),
        'name': PlutoCell(value: user.name),
        'email': PlutoCell(value: user.email),
        'role': PlutoCell(value: _getRoleText((user.role is UserRole ? user.role as UserRole : UserRole.user))),
        'status': PlutoCell(value: user.isActive ? 'نشط' : 'معطل'),
        'created_at': PlutoCell(value: _formatDate(user.createdAt)),
        'actions': PlutoCell(value: ''),
      });
    }).toList();
  }

  /// الحصول على المستخدم من صف PlutoGrid
  User? _getUserFromRow(PlutoRow row) {
    final userId = row.cells['id']?.value;
    if (userId == null) return null;
    return _filteredUsers.firstWhereOrNull((u) => u.id == userId) ??
           _adminController.users.firstWhereOrNull((u) => u.id == userId);
  }


  /// بناء جدول المستخدمين باستخدام PlutoGrid للشاشات الكبيرة
  Widget _buildUsersPlutoGrid() {
    return Obx(() {
      // إنشاء الأعمدة والصفوف
      final columns = _createPlutoColumns();
      final rows = _createPlutoRows();

      return PlutoGrid(
        columns: columns,
        rows: rows,
        onLoaded: (PlutoGridOnLoadedEvent event) {
          _plutoGridStateManager = event.stateManager;

          // تطبيق إعدادات PlutoGrid المتقدمة
          _plutoGridStateManager?.setConfiguration(
            PlutoGridConfiguration(
              style: const PlutoGridStyleConfig(
                gridBorderRadius: BorderRadius.all(Radius.circular(8)),
                enableGridBorderShadow: true,
                enableColumnBorderVertical: true,
                enableColumnBorderHorizontal: true,
                enableCellBorderVertical: true,
                enableCellBorderHorizontal: true,
                enableRowColorAnimation: true,
                gridBorderColor: Colors.grey,
                activatedBorderColor: Colors.blue,
                inactivatedBorderColor: Colors.grey,
                borderColor: Colors.grey,
                rowHeight: 50,
                columnHeight: 45,
                columnFilterHeight: 35,
                cellTextStyle: TextStyle(fontSize: 14),
                columnTextStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                iconSize: 18,
                defaultColumnTitlePadding: EdgeInsets.symmetric(horizontal: 8),
                defaultCellPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              columnSize: const PlutoGridColumnSizeConfig(
                autoSizeMode: PlutoAutoSizeMode.scale,
                resizeMode: PlutoResizeMode.pushAndPull,
                restoreAutoSizeAfterHideColumn: true,
                restoreAutoSizeAfterFrozenColumn: true,
                restoreAutoSizeAfterMoveColumn: true,
                restoreAutoSizeAfterInsertColumn: true,
                restoreAutoSizeAfterRemoveColumn: true,
              ),
              scrollbar: const PlutoGridScrollbarConfig(
                draggableScrollbar: true,
                isAlwaysShown: true,
                scrollbarThickness: 8,
                scrollbarThicknessWhileDragging: 10,
                scrollbarRadius: Radius.circular(4),
                scrollbarRadiusWhileDragging: Radius.circular(6),
              ),
              columnFilter: PlutoGridColumnFilterConfig(
                filters: const [
                  PlutoFilterTypeContains(),
                  PlutoFilterTypeEquals(),
                  PlutoFilterTypeStartsWith(),
                  PlutoFilterTypeEndsWith(),
                  PlutoFilterTypeGreaterThan(),
                  PlutoFilterTypeGreaterThanOrEqualTo(),
                  PlutoFilterTypeLessThan(),
                  PlutoFilterTypeLessThanOrEqualTo(),
                ],
              ),
              enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
              enableMoveDownAfterSelecting: true,
              enableMoveHorizontalInEditing: true,
              localeText: PlutoGridLocaleText.arabic(),
            ),
          );

          // تفعيل التصفية
          _plutoGridStateManager?.setShowColumnFilter(true);

          // تطبيق التجميع إذا كان مفعلاً
          if (_isGroupingEnabled.value && _groupedColumns.isNotEmpty) {
            _applyGrouping();
          }
        },
        onChanged: (PlutoGridOnChangedEvent event) {
          // التعامل مع تغيير البيانات
          _handleCellChange(event);
        },
        onSorted: (PlutoGridOnSortedEvent event) {
          // التعامل مع الترتيب
          debugPrint('تم ترتيب العمود: ${event.column.field}');
        },
        onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
          // فتح نافذة التحرير عند النقر المزدوج
          final user = _getUserFromRow(event.row);
          if (user != null) {
            _showUserDialog(user: user);
          }
        },
        createHeader: (stateManager) {
          return Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.people, color: AppColors.primary),
                const SizedBox(width: 8),
                Obx(() => Text(
                  'إدارة المستخدمين - ${_filteredUsers.length} مستخدم${_isGroupingEnabled.value ? ' (مجمع)' : ''}',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                )),
                // عرض أعمدة التجميع النشطة
                Obx(() => _isGroupingEnabled.value && _groupedColumns.isNotEmpty
                    ? Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.group, size: 16, color: Colors.blue),
                            const SizedBox(width: 4),
                            Text(
                              'مجمع حسب: ${_groupedColumns.map((col) => col.title).join(', ')}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink()),
                const Spacer(),
                // زر تصدير البيانات
                IconButton(
                  icon: const Icon(Icons.download),
                  tooltip: 'تصدير البيانات',
                  onPressed: _exportUsersData,
                ),
              ],
            ),
          );
        },
      );
    });
  }

  /// التعامل مع تغيير خلايا PlutoGrid
  void _handleCellChange(PlutoGridOnChangedEvent event) {
    final user = _getUserFromRow(event.row);
    if (user == null) {
      debugPrint('خطأ: لم يتم العثور على المستخدم للصف المحدد');
      return;
    }

    final field = event.column.field;
    final newValue = event.value;

    debugPrint('تم تغيير الخلية: $field = $newValue للمستخدم: ${user.name}');

    // التعامل مع التغييرات حسب نوع الحقل
    switch (field) {
      case 'name':
        _updateUserField(user, 'name', newValue);
        break;
      case 'email':
        _updateUserField(user, 'email', newValue);
        break;
      case 'role':
        _updateUserRole(user, newValue);
        break;
      case 'status':
        _updateUserStatus(user, newValue);
        break;
    }
  }

  /// تحديث حقل المستخدم
  void _updateUserField(User user, String field, dynamic value) async {
    try {
      User updatedUser;
      switch (field) {
        case 'name':
          updatedUser = user.copyWith(name: value.toString());
          break;
        case 'email':
          updatedUser = user.copyWith(email: value.toString());
          break;
        default:
          return;
      }

      final result = await _adminController.updateUser(updatedUser);
      if (result) {
        await _refreshData();
        Get.snackbar(
          'تم التحديث',
          'تم تحديث $field بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث $field: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تحديث دور المستخدم
  void _updateUserRole(User user, String roleText) async {
    // استخدم الدالة الموحدة للتحويل من النص العربي إلى UserRole
    final newRole = UserRole.fromArabic(roleText);
    try {
      // تمرير roleId فقط وليس role
      final updatedUser = user.copyWith(roleId: newRole.value);
      final result = await _adminController.updateUser(updatedUser);
      if (result) {
        await _refreshData();
        Get.snackbar(
          'تم التحديث',
          'تم تحديث دور المستخدم بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث دور المستخدم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تحديث حالة المستخدم
  void _updateUserStatus(User user, String statusText) async {
    final isActive = statusText == 'نشط';
    if (user.isActive != isActive) {
      _toggleUserActive(user);
    }
  }

  /// تصدير بيانات المستخدمين
  void _exportUsersData() async {
    try {
      Get.snackbar(
        'تصدير البيانات',
        'جاري تصدير بيانات المستخدمين...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // هنا يمكن إضافة منطق تصدير البيانات الفعلي
      // مثل تصدير إلى CSV أو Excel
      await Future.delayed(const Duration(seconds: 1));

      Get.snackbar(
        'تم التصدير',
        'تم تصدير ${_filteredUsers.length} مستخدم بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ في التصدير',
        'فشل في تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تبديل حالة التجميع
  void _toggleGrouping() {
    _isGroupingEnabled.value = !_isGroupingEnabled.value;

    if (_isGroupingEnabled.value) {
      // تفعيل التجميع حسب الدور كافتراضي
      _enableGroupingByRole();
    } else {
      // إلغاء التجميع
      _disableGrouping();
    }
  }

  /// تفعيل التجميع حسب الدور
  void _enableGroupingByRole() {
    if (_plutoGridStateManager != null) {
      final roleColumn = _plutoGridStateManager!.columns
          .firstWhere((col) => col.field == 'role');

      _groupedColumns.clear();
      _groupedColumns.add(roleColumn);

      _applyGrouping();

      Get.snackbar(
        'تم تفعيل التجميع',
        'تم تجميع المستخدمين حسب الدور',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// إلغاء التجميع
  void _disableGrouping() {
    if (_plutoGridStateManager != null) {
      _groupedColumns.clear();
      _plutoGridStateManager!.setRowGroup(null);

      Get.snackbar(
        'تم إلغاء التجميع',
        'تم إلغاء تجميع المستخدمين',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// تطبيق التجميع
  void _applyGrouping() {
    if (_plutoGridStateManager != null && _groupedColumns.isNotEmpty) {
      _plutoGridStateManager!.setRowGroup(
        PlutoRowGroupByColumnDelegate(
          columns: _groupedColumns,
          showFirstExpandableIcon: false,
          showCount: true,
          enableCompactCount: true,
        ),
      );
    }
  }

  /// عرض حوار اختيار أعمدة التجميع
  void _showGroupingDialog() {
    final availableColumns = _plutoGridStateManager?.columns
        .where((col) => col.field != 'id' &&
                       col.field != 'profile_image' &&
                       col.field != 'actions')
        .toList() ?? [];

    Get.dialog(
      AlertDialog(
        title: const Text('اختيار أعمدة التجميع'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: Column(
            children: [
              const Text('اختر الأعمدة التي تريد التجميع حسبها:'),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: availableColumns.length,
                  itemBuilder: (context, index) {
                    final column = availableColumns[index];
                    return Obx(() => CheckboxListTile(
                      title: Text(column.title),
                      value: _groupedColumns.contains(column),
                      onChanged: (bool? value) {
                        if (value == true) {
                          if (!_groupedColumns.contains(column)) {
                            _groupedColumns.add(column);
                          }
                        } else {
                          _groupedColumns.remove(column);
                        }
                      },
                    ));
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (_groupedColumns.isNotEmpty) {
                _isGroupingEnabled.value = true;
                _applyGrouping();
                Get.snackbar(
                  'تم تطبيق التجميع',
                  'تم تجميع البيانات حسب الأعمدة المحددة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                _isGroupingEnabled.value = false;
                _disableGrouping();
              }
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين للشاشات الصغيرة
  Widget _buildUsersMobileList() {
    return ListView.builder(
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: ImageHelper.buildProfileImage(
              imagePath: user.profileImage,
              radius: 20,
              fallbackText: user.name,
              backgroundColor: AppColors.primary.withAlpha(51),
              textColor: AppColors.primary,
            ),
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                Text('الدور: ${_getRoleText((user.role is UserRole ? user.role as UserRole : UserRole.user))}'),
                Row(
                  children: [
                    Icon(
                      user.isActive ? Icons.check_circle : Icons.cancel,
                      color: user.isActive ? Colors.green : Colors.red,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      user.isActive ? 'نشط' : 'معطل',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showUserDialog(user: user);
                    break;
                  case 'toggle':
                    _toggleUserActive(user);
                    break;
                  case 'permissions':
                    // الانتقال إلى تبويب إدارة الصلاحيات للمستخدم
                    if (widget.onGoToUserPermissions != null) {
                      widget.onGoToUserPermissions!(user);
                    }
                    break;
                  case 'view_permissions':
                    // الانتقال إلى شاشة عرض صلاحيات المستخدم التفصيلية
                    Get.toNamed('/admin/user-permissions', arguments: {'userId': user.id});
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'toggle',
                  child: Row(
                    children: [
                      Icon(
                        user.isActive ? Icons.block : Icons.check_circle,
                        color: user.isActive ? Colors.red : Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(user.isActive ? 'تعطيل' : 'تفعيل'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'permissions',
                  child: Row(
                    children: [
                      Icon(Icons.admin_panel_settings, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('إدارة الصلاحيات'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'view_permissions',
                  child: Row(
                    children: [
                      Icon(Icons.visibility, color: Colors.green),
                      SizedBox(width: 8),
                      Text('عرض الصلاحيات التفصيلية'),
                    ],
                  ),
                ),
              ],
            ),
            onTap: () => _showUserDetailsDialog(user),
          ),
        );
      },
    );
  }

  /// عرض خيارات اختيار الصورة
  Future<void> _showImagePickerOptions(
      BuildContext context, Function(File) onImageSelected) async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery(onImageSelected);
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera(onImageSelected);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// معالجة الصورة وضغطها
  Future<File> _processImage(File imageFile) async {
    try {
      // استخدام FileProcessor لإنشاء صورة مصغرة
      final thumbnailPath = await FileProcessor.createThumbnail(
        imagePath: imageFile.path,
        thumbnailSize: 500, // حجم مناسب لصورة الملف الشخصي
      );

      if (thumbnailPath != null) {
        return File(thumbnailPath);
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
    }

    // إذا فشلت المعالجة، إرجاع الصورة الأصلية
    return imageFile;
  }

  /// عرض حوار تفاصيل المستخدم
  void _showUserDetailsDialog(User user) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل المستخدم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المستخدم
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: ImageHelper.buildProfileImage(
                    imagePath: user.profileImage,
                    radius: 60,
                    fallbackText: user.name,
                    backgroundColor: AppColors.primary.withAlpha(51),
                    textColor: AppColors.primary,
                    fallbackIcon: Icons.person,
                  ),
                ),
              ),
              _buildDetailItem('الاسم', user.name),
              _buildDetailItem('البريد الإلكتروني', user.email),
              _buildDetailItem('الدور', _getRoleText((user.role is UserRole ? user.role as UserRole : UserRole.user))),
              _buildDetailItem('الحالة', user.isActive ? 'نشط' : 'معطل'),
              _buildDetailItem('تاريخ الإنشاء', _formatDate(user.createdAt)),
              _buildDetailItem(
                'آخر تسجيل دخول',
                user.lastLogin != null
                    ? _formatDate(user.lastLogin!)
                    : 'لم يسجل الدخول',
              ),
              if (user.departmentId != null)
                _buildDetailItem('القسم', user.departmentId.toString()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _showUserDialog(user: user);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  /// تبديل حالة نشاط المستخدم
  void _toggleUserActive(User user) async {
    try {
      final result = await _adminController.toggleUserActive(user.id, !user.isActive);

      if (result) {
        // تحديث القائمة المحلية فوراً
        await _refreshData();

        Get.snackbar(
          'نجح',
          'تم تحديث حالة المستخدم بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة المستخدم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل تغيير حالة المستخدم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// بناء عنصر تفاصيل
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الدور
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'مدير النظام';
      case UserRole.admin:
        return 'مدير عام';
      case UserRole.manager:
        return 'مدير';
      case UserRole.supervisor:
        return 'مشرف';
      case UserRole.user:
        return 'مستخدم';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // مهم جداً لـ AutomaticKeepAliveClientMixin

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة المستخدمين'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'قائمة المستخدمين'),
              Tab(text: 'تقرير المستخدمين'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // قائمة المستخدمين
            OrientationBuilder(
              builder: (context, orientation) {
                return Column(
                  children: [
                    // شريط البحث
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          labelText: 'ابحث عن مستخدم',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: Colors.grey.shade200,
                        ),
                        onChanged: (value) {
                          _filterUsers();
                        },
                      ),
                    ),
                    // زر إضافة مستخدم جديد
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            _showUserDialog();
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة مستخدم جديد'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // عرض الجدول أو القائمة حسب حجم الشاشة
                    Expanded(
                      child: orientation == Orientation.portrait
                          ? _buildUsersMobileList() // عرض القائمة في الوضع العمودي
                          : _buildUsersPlutoGrid(), // عرض الجدول في الوضع الأفقي
                    ),
                  ],
                );
              },
            ),
            // تقرير المستخدمين
            Center(
              child: Text('تقرير المستخدمين سيظهر هنا'),
            ),
          ],
        ),
      ),
    );
  }
}
