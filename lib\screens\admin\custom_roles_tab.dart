import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:get/get.dart';


// ودجت موحد لعرض الدور
import 'create_role_dialog.dart'; // حوار إنشاء دور
import '../../widgets/admin_filter_widget.dart';
import 'permission_role_card_widget.dart';

class CustomRolesTab extends StatelessWidget {
  const CustomRolesTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NewPermissionsController>();

    return Obx(() {
      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'خطأ: ${controller.error}',
                style: TextStyle(color: Colors.red[700]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.refresh(),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      return Column(
        children: [
          // فلتر بحث وفئة للأدوار
          AdminFilterWidget(
            searchLabel: 'بحث عن دور...',
            searchValue: controller.searchQuery,
            onSearchChanged: (val) => controller.searchPermissions(val),
            dropdownLabel: 'المجموعة',
            dropdownValue: controller.selectedGroup,
            dropdownItems: [
              const DropdownMenuItem(value: null, child: Text('الكل')),
              ...controller.permissionGroups.map((g) => DropdownMenuItem(value: g, child: Text(g))).toList(),
            ],
            onDropdownChanged: (val) => controller.setGroupFilter(val),
          ),
          Expanded(
            child: Row(
              children: [
                // قائمة الأدوار
                SizedBox(
                  width: 300,
                  child: Card(
                    margin: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'الأدوار المخصصة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.add),
                                tooltip: 'إنشاء دور جديد',
                                onPressed: () => CreateRoleDialog.show(context, controller),
                              ),
                            ],
                          ),
                        ),
                        const Divider(),
                        Expanded(
                          child: controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').isEmpty
                              ? const Center(
                                  child: Text(
                                    'لا توجد أدوار مخصصة\nانقر على + لإنشاء دور جديد',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                )
                              : ListView.builder(
                                  itemCount: controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').length,
                                  itemBuilder: (context, index) {
                                    final role = controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').toList()[index];
                                    return PermissionRoleCardWidget(
                                      role: role,
                                      isSelected: controller.currentPermission?.id == role.id,
                                      onTap: () => controller.getPermissionById(role.id),
                                      onEdit: () => CreateRoleDialog.show(context, controller, role: role),
                                      onDelete: () async {
                                        // منطق الحذف (يمكن تخصيصه)
                                        final confirm = await showDialog<bool>(
                                          context: context,
                                          builder: (ctx) => AlertDialog(
                                            title: const Text('تأكيد الحذف'),
                                            content: Text('هل أنت متأكد من حذف الدور: ${role.name}؟'),
                                            actions: [
                                              TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
                                              ElevatedButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
                                            ],
                                          ),
                                        );
                                        if (confirm == true) {
                                          await controller.deletePermission(role.id);
                                        }
                                      },
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                ),

                // تفاصيل الدور المحدد
                Expanded(
                  child: Card(
                    margin: const EdgeInsets.all(8),
                    child: Obx(() {
                      final selectedRole = controller.currentPermission;
                      if (selectedRole == null) {
                        return const Center(
                          child: Text(
                            'اختر دوراً من القائمة لعرض تفاصيله',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        );
                      }

                      return PermissionRoleCardWidget(
                        role: selectedRole,
                        isDetailed: true,
                        onEdit: () => CreateRoleDialog.show(context, controller, role: selectedRole),
                        onDelete: () async {
                          final confirm = await showDialog<bool>(
                            context: context,
                            builder: (ctx) => AlertDialog(
                              title: const Text('تأكيد الحذف'),
                              content: Text('هل أنت متأكد من حذف الدور: ${selectedRole.name}؟'),
                              actions: [
                                TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
                                ElevatedButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
                              ],
                            ),
                          );
                          if (confirm == true) {
                            await controller.deletePermission(selectedRole.id);
                          }
                        },
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}