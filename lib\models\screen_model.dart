class Screen {
  final int id;
  final String name;
  final String? description;
  final bool isActive;
  final int createdAt;
  final int updatedAt;

  const Screen({
    required this.id,
    required this.name,
    this.description,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Screen.fromJson(Map<String, dynamic> json) {
    return Screen(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int,
    );
  }

  Map<String, dynamic> toJson() =>
      {'id': id, 'name': name, 'description': description, 'isActive': isActive, 'createdAt': createdAt, 'updatedAt': updatedAt};
}