import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/role_model.dart';
import '../models/permission_models.dart';
import '../models/department_model.dart';
import '../models/system_models.dart';
import '../models/activity_log_models.dart';
import '../models/system_setting_models.dart' as setting_models;
import '../services/api/unified_api_services.dart';
import '../services/unified_permission_service.dart';

/// متحكم الإدارة الموحد والمحسن
/// 
/// يوفر إدارة شاملة وديناميكية لجميع جوانب النظام الإداري
/// - إدارة المستخدمين والأدوار والصلاحيات
/// - إدارة الأقسام والشاشات والإجراءات
/// - إدارة النظام والنسخ الاحتياطية والتقارير
/// - معالجة محسنة للأخطاء وإدارة الحالة
class AdminController extends GetxController {
  static AdminController get instance => Get.find<AdminController>();
  
  // ===== الخدمات =====
  final UsersApiService _usersApiService = UsersApiService();
  final PermissionsApiService _permissionsApiService = PermissionsApiService();
  final ActivityLogsApiService _activityLogsApiService = ActivityLogsApiService();
  final BackupsApiService _backupsApiService = BackupsApiService();
  final SystemSettingsApiService _systemSettingsApiService = SystemSettingsApiService();
  final ApiService _apiService = ApiService();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  // ===== البيانات الأساسية =====
  
  // المستخدمون والأدوار والصلاحيات
  final RxList<User> _users = <User>[].obs;
  final RxList<Role> _roles = <Role>[].obs;
  final RxList<Permission> _permissions = <Permission>[].obs;
  final RxList<Department> _departments = <Department>[].obs;
  
  // النظام والسجلات
  final RxList<SystemLog> _systemLogs = <SystemLog>[].obs;
  final RxList<ActivityLog> _activityLogs = <ActivityLog>[].obs;
  final RxList<Backup> _backups = <Backup>[].obs;
  final RxList<setting_models.SystemSetting> _systemSettings = <setting_models.SystemSetting>[].obs;

  // ===== حالة التحميل والأخطاء =====
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingUsers = false.obs;
  final RxBool _isLoadingRoles = false.obs;
  final RxBool _isLoadingPermissions = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // ===== المتغيرات المساعدة =====
  final Rx<User?> _selectedUser = Rx<User?>(null);
  final Rx<Role?> _selectedRole = Rx<Role?>(null);
  final RxMap<String, dynamic> _statistics = <String, dynamic>{}.obs;

  // ===== Getters للبيانات =====
  List<User> get users => _users;
  List<Role> get roles => _roles;
  List<Permission> get permissions => _permissions;
  List<Department> get departments => _departments;
  List<SystemLog> get systemLogs => _systemLogs;
  List<ActivityLog> get activityLogs => _activityLogs;
  List<Backup> get backups => _backups;
  List<setting_models.SystemSetting> get systemSettings => _systemSettings;

  // ===== Getters للحالة =====
  bool get isLoading => _isLoading.value;
  bool get isLoadingUsers => _isLoadingUsers.value;
  bool get isLoadingRoles => _isLoadingRoles.value;
  bool get isLoadingPermissions => _isLoadingPermissions.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  User? get selectedUser => _selectedUser.value;
  Role? get selectedRole => _selectedRole.value;
  Map<String, dynamic> get statistics => _statistics;

  // ===== Getters للإحصائيات =====
  int get totalUsers => _users.length;
  int get activeUsers => _users.where((u) => u.isActive).length;
  int get totalRoles => _roles.length;
  int get activeRoles => _roles.where((r) => r.isActive).length;
  int get totalPermissions => _permissions.length;

  @override
  void onInit() {
    super.onInit();
    debugPrint('🚀 تم تهيئة AdminController');
    _initializeData();
  }

  /// تهيئة البيانات الأساسية
  Future<void> _initializeData() async {
    try {
      await Future.wait([
        loadUsers(),
        loadRoles(),
        loadPermissions(),
        loadDepartments(),
      ]);
      debugPrint('✅ تم تحميل جميع البيانات الأساسية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة البيانات: $e');
    }
  }

  // ===== إدارة المستخدمين =====

  /// تحميل جميع المستخدمين مع تحسين الأداء
  Future<void> loadUsers({
    bool includeInactive = false,
    int? departmentId,
    int? roleId,
    int page = 1,
    int pageSize = 50,
    bool clearExisting = true,
  }) async {
    if (_isLoadingUsers.value) return;

    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل المستخدمين (الصفحة $page)...');

      // استخدام معاملات محسنة للأداء
      final users = await _usersApiService.getAllUsers();

      if (clearExisting || page == 1) {
        // إذا كانت الصفحة الأولى أو طُلب مسح البيانات الموجودة
        _users.assignAll(users);
      } else {
        // إضافة للبيانات الموجودة (للصفحات التالية)
        _users.addAll(users);
      }

      _updateStatistics();
      debugPrint('✅ تم تحميل ${users.length} مستخدم (إجمالي: ${_users.length})');

      // تنظيف الذاكرة - إزالة المراجع غير المستخدمة
      if (_users.length > 1000) {
        debugPrint('⚠️ تحذير: عدد كبير من المستخدمين في الذاكرة (${_users.length})');
      }

    } catch (e) {
      _error.value = 'خطأ في تحميل المستخدمين: $e';
      debugPrint('❌ خطأ في تحميل المستخدمين: $e');
      if (clearExisting) {
        _users.clear();
      }
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// تحميل المزيد من المستخدمين (للتمرير اللانهائي)
  Future<void> loadMoreUsers() async {
    if (!_isLoadingUsers.value) {
      await loadUsers(
        page: (_users.length ~/ 50) + 1,
        clearExisting: false,
      );
    }
  }

  /// تحديث قائمة المستخدمين مع الاحتفاظ بالفلاتر
  Future<void> refreshUsers() async {
    await loadUsers(clearExisting: true);
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(User user) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري إنشاء مستخدم جديد...');
      final newUser = await _usersApiService.createUser(user);
      _users.add(newUser);
      _updateStatistics();
      _successMessage.value = 'تم إنشاء المستخدم بنجاح';
      debugPrint('✅ تم إنشاء مستخدم جديد: ${newUser.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المستخدم: $e';
      debugPrint('❌ خطأ في إنشاء المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحديث المستخدم...');
      final updatedUser = await _usersApiService.updateUser(user.id, user);
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = updatedUser;
        _updateStatistics();
        _successMessage.value = 'تم تحديث المستخدم بنجاح';
        debugPrint('✅ تم تحديث المستخدم: ${updatedUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المستخدم: $e';
      debugPrint('❌ خطأ في تحديث المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري حذف المستخدم...');
      final success = await _usersApiService.deleteUser(userId);
      if (success) {
        _users.removeWhere((u) => u.id == userId);
        _updateStatistics();
        _successMessage.value = 'تم حذف المستخدم بنجاح';
        debugPrint('✅ تم حذف المستخدم: $userId');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المستخدم: $e';
      debugPrint('❌ خطأ في حذف المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  // ===== إدارة الأدوار =====

  /// تحميل جميع الأدوار
  Future<void> loadRoles() async {
    _isLoadingRoles.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل الأدوار...');
      final response = await _apiService.get('/api/Roles');
      final data = jsonDecode(response.body) as List;
      final roles = data.map((json) => Role.fromJson(json)).toList();
      _roles.assignAll(roles);
      _updateStatistics();
      debugPrint('✅ تم تحميل ${roles.length} دور');
    } catch (e) {
      _error.value = 'خطأ في تحميل الأدوار: $e';
      debugPrint('❌ خطأ في تحميل الأدوار: $e');
      _roles.clear();
    } finally {
      _isLoadingRoles.value = false;
    }
  }

  /// تبديل حالة الدور (تفعيل/إلغاء تفعيل)
  Future<bool> toggleRoleStatus(int roleId, bool isActive) async {
    try {
      debugPrint('🔄 جاري تحديث حالة الدور...');
      final response = await _apiService.put('/api/Roles/$roleId/status', {
        'isActive': isActive,
      });

      if (response.statusCode == 200) {
        debugPrint('✅ تم تحديث حالة الدور بنجاح');
        return true;
      } else {
        throw Exception('فشل في تحديث حالة الدور: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث حالة الدور: $e');
      _error.value = 'خطأ في تحديث حالة الدور: $e';
      return false;
    }
  }

  /// حذف دور
  Future<bool> deleteRole(int roleId) async {
    try {
      debugPrint('🔄 جاري حذف الدور...');
      final response = await _apiService.delete('/api/Roles/$roleId');

      if (response.statusCode == 200) {
        debugPrint('✅ تم حذف الدور بنجاح');
        return true;
      } else {
        throw Exception('فشل في حذف الدور: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الدور: $e');
      _error.value = 'خطأ في حذف الدور: $e';
      return false;
    }
  }

  /// تحسين قاعدة البيانات
  Future<bool> optimizeDatabase() async {
    try {
      debugPrint('🔄 جاري تحسين قاعدة البيانات...');
      final response = await _apiService.post('/api/System/optimize-database', {});

      if (response.statusCode == 200) {
        debugPrint('✅ تم تحسين قاعدة البيانات بنجاح');
        return true;
      } else {
        throw Exception('فشل في تحسين قاعدة البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحسين قاعدة البيانات: $e');
      _error.value = 'خطأ في تحسين قاعدة البيانات: $e';
      return false;
    }
  }

  /// إصلاح قاعدة البيانات
  Future<bool> repairDatabase() async {
    try {
      debugPrint('🔄 جاري إصلاح قاعدة البيانات...');
      final response = await _apiService.post('/api/System/repair-database', {});

      if (response.statusCode == 200) {
        debugPrint('✅ تم إصلاح قاعدة البيانات بنجاح');
        return true;
      } else {
        throw Exception('فشل في إصلاح قاعدة البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إصلاح قاعدة البيانات: $e');
      _error.value = 'خطأ في إصلاح قاعدة البيانات: $e';
      return false;
    }
  }

  /// إعادة تشغيل النظام
  Future<bool> restartSystem() async {
    try {
      debugPrint('🔄 جاري إعادة تشغيل النظام...');
      final response = await _apiService.post('/api/System/restart', {});

      if (response.statusCode == 200) {
        debugPrint('✅ تم إعادة تشغيل النظام بنجاح');
        return true;
      } else {
        throw Exception('فشل في إعادة تشغيل النظام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تشغيل النظام: $e');
      _error.value = 'خطأ في إعادة تشغيل النظام: $e';
      return false;
    }
  }

  // ===== إدارة الصلاحيات =====

  /// تحميل جميع الصلاحيات
  Future<void> loadPermissions() async {
    _isLoadingPermissions.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل الصلاحيات...');
      final permissions = await _permissionsApiService.getAllPermissions();
      _permissions.assignAll(permissions);
      _updateStatistics();
      debugPrint('✅ تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('❌ خطأ في تحميل الصلاحيات: $e');
      _permissions.clear();
    } finally {
      _isLoadingPermissions.value = false;
    }
  }

  // ===== إدارة الأقسام =====

  /// تحميل جميع الأقسام
  Future<void> loadDepartments() async {
    try {
      debugPrint('🔄 جاري تحميل الأقسام...');
      final response = await _apiService.get('/api/Departments');
      final data = jsonDecode(response.body) as List;
      final departments = data.map((json) => Department.fromJson(json)).toList();
      _departments.assignAll(departments);
      debugPrint('✅ تم تحميل ${departments.length} قسم');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
      _departments.clear();
    }
  }

  /// تحديث الإحصائيات
  void _updateStatistics() {
    _statistics.assignAll({
      'totalUsers': totalUsers,
      'activeUsers': activeUsers,
      'totalRoles': totalRoles,
      'activeRoles': activeRoles,
      'totalPermissions': totalPermissions,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// مسح الأخطاء والرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// تحديد المستخدم المحدد
  void selectUser(User? user) {
    _selectedUser.value = user;
  }

  /// تحديد الدور المحدد
  void selectRole(Role? role) {
    _selectedRole.value = role;
  }

  /// التحقق من وجود بيانات أساسية
  bool get hasEssentialData => _users.isNotEmpty || _systemSettings.isNotEmpty;

  /// التحقق من وجود أخطاء
  bool get hasError => _error.value.isNotEmpty;

  /// التحقق من وجود رسائل نجاح
  bool get hasSuccessMessage => _successMessage.value.isNotEmpty;

  /// تحميل سجلات النظام
  Future<void> loadSystemLogs() async {
    try {
      debugPrint('🔄 جاري تحميل سجلات النظام...');
      final response = await _apiService.get('/api/SystemLogs');
      final data = jsonDecode(response.body) as List;
      final logs = data.map((json) => SystemLog.fromJson(json)).toList();
      _systemLogs.assignAll(logs);
      debugPrint('✅ تم تحميل ${logs.length} سجل نظام');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجلات النظام: $e');
      _systemLogs.clear();
    }
  }

  /// تحميل سجلات النشاط
  Future<void> loadActivityLogs() async {
    try {
      debugPrint('🔄 جاري تحميل سجلات النشاط...');
      final logs = await _activityLogsApiService.getAllActivityLogs();
      _activityLogs.assignAll(logs);
      debugPrint('✅ تم تحميل ${logs.length} سجل نشاط');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجلات النشاط: $e');
      _activityLogs.clear();
    }
  }

  /// تحميل النسخ الاحتياطية
  Future<void> loadBackups() async {
    try {
      debugPrint('🔄 جاري تحميل النسخ الاحتياطية...');
      final backups = await _backupsApiService.getAllBackups();
      _backups.assignAll(backups);
      debugPrint('✅ تم تحميل ${backups.length} نسخة احتياطية');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل النسخ الاحتياطية: $e');
      _backups.clear();
    }
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<bool> createBackup(String description) async {
    try {
      debugPrint('🔄 جاري إنشاء نسخة احتياطية...');
      final backup = await _backupsApiService.createBackup(description);
      _backups.insert(0, backup); // إضافة في المقدمة
      debugPrint('✅ تم إنشاء النسخة الاحتياطية: ${backup.name}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// استعادة نسخة احتياطية
  Future<bool> restoreBackup(int backupId) async {
    try {
      debugPrint('🔄 جاري استعادة النسخة الاحتياطية...');
      await _backupsApiService.restoreBackup(backupId);
      debugPrint('✅ تم استعادة النسخة الاحتياطية بنجاح');

      // إعادة تحميل جميع البيانات بعد الاستعادة
      await refreshAllData();
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة النسخة الاحتياطية: $e');
      _error.value = 'خطأ في استعادة النسخة الاحتياطية: $e';
      return false;
    }
  }

  /// تحميل إعدادات النظام
  Future<void> loadSystemSettings() async {
    try {
      debugPrint('🔄 جاري تحميل إعدادات النظام...');
      final settings = await _systemSettingsApiService.getAllSettings();
      _systemSettings.assignAll(settings);
      debugPrint('✅ تم تحميل ${settings.length} إعداد نظام');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات النظام: $e');
      _systemSettings.clear();
    }
  }

  /// تحديث جميع البيانات
  Future<void> refreshAllData() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await Future.wait([
        loadUsers(),
        loadRoles(),
        loadPermissions(),
        loadDepartments(),
        loadSystemLogs(),
        loadActivityLogs(),
        loadBackups(),
        loadSystemSettings(),
      ]);
      _updateStatistics();
      _successMessage.value = 'تم تحديث جميع البيانات بنجاح';
      debugPrint('✅ تم تحديث جميع البيانات الإدارية');
    } catch (e) {
      _error.value = 'خطأ في تحديث البيانات: $e';
      debugPrint('❌ خطأ في تحديث البيانات الإدارية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تنظيف البيانات القديمة لتحسين الأداء
  void cleanupOldData() {
    try {
      // تنظيف السجلات القديمة (الاحتفاظ بآخر 100 سجل فقط)
      if (_systemLogs.length > 100) {
        final logsToKeep = _systemLogs.take(100).toList();
        _systemLogs.assignAll(logsToKeep);
        debugPrint('🧹 تم تنظيف السجلات القديمة (الاحتفاظ بـ 100 سجل)');
      }

      if (_activityLogs.length > 100) {
        final logsToKeep = _activityLogs.take(100).toList();
        _activityLogs.assignAll(logsToKeep);
        debugPrint('🧹 تم تنظيف سجلات النشاط القديمة (الاحتفاظ بـ 100 سجل)');
      }

      // تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 20 نسخة)
      if (_backups.length > 20) {
        final backupsToKeep = _backups.take(20).toList();
        _backups.assignAll(backupsToKeep);
        debugPrint('🧹 تم تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بـ 20 نسخة)');
      }

      debugPrint('✅ تم تنظيف البيانات القديمة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات القديمة: $e');
    }
  }

  /// مراقبة استخدام الذاكرة
  void monitorMemoryUsage() {
    final totalItems = _users.length +
                      _roles.length +
                      _permissions.length +
                      _departments.length +
                      _systemLogs.length +
                      _activityLogs.length +
                      _backups.length +
                      _systemSettings.length;

    debugPrint('📊 إحصائيات الذاكرة:');
    debugPrint('   - المستخدمون: ${_users.length}');
    debugPrint('   - الأدوار: ${_roles.length}');
    debugPrint('   - الصلاحيات: ${_permissions.length}');
    debugPrint('   - الأقسام: ${_departments.length}');
    debugPrint('   - سجلات النظام: ${_systemLogs.length}');
    debugPrint('   - سجلات النشاط: ${_activityLogs.length}');
    debugPrint('   - النسخ الاحتياطية: ${_backups.length}');
    debugPrint('   - إعدادات النظام: ${_systemSettings.length}');
    debugPrint('   - إجمالي العناصر: $totalItems');

    // تحذير إذا كان الاستخدام مرتفع
    if (totalItems > 5000) {
      debugPrint('⚠️ تحذير: استخدام مرتفع للذاكرة ($totalItems عنصر)');
      debugPrint('💡 يُنصح بتنظيف البيانات القديمة');
    }
  }

  @override
  void onClose() {
    debugPrint('🔄 تنظيف AdminController...');

    // تنظيف القوائم لتحرير الذاكرة
    _users.clear();
    _roles.clear();
    _permissions.clear();
    _departments.clear();
    _systemLogs.clear();
    _activityLogs.clear();
    _backups.clear();
    _systemSettings.clear();

    debugPrint('✅ تم تنظيف جميع البيانات من الذاكرة');
    super.onClose();
  }
}
