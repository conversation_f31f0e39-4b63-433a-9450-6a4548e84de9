import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:flutter_application_2/controllers/user_permissions_controller.dart';
import 'package:get/get.dart';
import '../../models/permission_models.dart';
import '../../controllers/admin_controller.dart';
import 'create_role_dialog.dart';
import 'permission_role_card_widget.dart';

/// شاشة إدارة الأدوار والصلاحيات المحسنة
class EnhancedPermissionsManagementScreen extends StatefulWidget {
  final bool isTab;

  const EnhancedPermissionsManagementScreen({
    super.key,
    this.isTab = false,
  });

  @override
  State<EnhancedPermissionsManagementScreen> createState() =>
      _EnhancedPermissionsManagementScreenState();
}

class _EnhancedPermissionsManagementScreenState
    extends State<EnhancedPermissionsManagementScreen>
    with TickerProviderStateMixin {
  late final NewPermissionsController _controller;
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(NewPermissionsController());
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isTab
          ? null
          : AppBar(
              title: const Text('إدارة الأدوار والصلاحيات المحسنة'),
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
      body: Column(
        children: [
          // شريط التبويبات
          Container(
            color: Theme.of(context).primaryColor.withAlpha(25),
            child: TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(
                  icon: Icon(Icons.group),
                  text: 'الأدوار المخصصة',
                ),
                Tab(
                  icon: Icon(Icons.security),
                  text: 'صلاحيات الواجهات',
                ),
                Tab(
                  icon: Icon(Icons.person),
                  text: 'صلاحيات المستخدمين',
                ),
              ],
            ),
          ),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCustomRolesTab(),
                _buildInterfacePermissionsTab(),
                _buildUserPermissionsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب الأدوار المخصصة
  Widget _buildCustomRolesTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_controller.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'خطأ: ${_controller.error}',
                style: TextStyle(color: Colors.red[700]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _controller.refresh(),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      return Row(
        children: [
          // قائمة الأدوار
          SizedBox(
            width: 300,
            child: Card(
              margin: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الأدوار المخصصة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          tooltip: 'إنشاء دور جديد',
                          onPressed: _showCreateRoleDialog,
                        ),
                      ],
                    ),
                  ),
                  const Divider(),
                  Expanded(
                    child: _controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد أدوار مخصصة\nانقر على + لإنشاء دور جديد',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey),
                            ),
                          )
                        : ListView.builder(
                            itemCount: _controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').length,
                            itemBuilder: (context, index) {
                              final role = _controller.allPermissions.where((p) => p.permissionGroup == 'CustomRoles').toList()[index];
                              return _buildRoleListItem(role);
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),

          // تفاصيل الدور المحدد
          Expanded(
            child: Card(
              margin: const EdgeInsets.all(8),
              child: Obx(() {
                final selectedRole = _controller.currentPermission;
                if (selectedRole == null) {
                  return const Center(
                    child: Text(
                      'اختر دوراً من القائمة لعرض تفاصيله',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  );
                }

                return _buildRoleDetails(selectedRole);
              }),
            ),
          ),
        ],
      );
    });
  }

  /// بناء عنصر الدور في القائمة
  Widget _buildRoleListItem(Permission role) {
    return Obx(() {
      final isSelected = _controller.currentPermission?.id == role.id;
      return PermissionRoleCardWidget(
        role: role,
        isSelected: isSelected,
        onTap: () => _controller.getPermissionById(role.id),
        onEdit: () => CreateRoleDialog.show(context, _controller, role: role),
        onDelete: () async {
          final confirm = await showDialog<bool>(
            context: context,
            builder: (ctx) => AlertDialog(
              title: const Text('تأكيد الحذف'),
              content: Text('هل أنت متأكد من حذف الدور: {role.name}؟'),
              actions: [
                TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
                ElevatedButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
              ],
            ),
          );
          if (confirm == true) {
            await _controller.deletePermission(role.id);
          }
        },
      );
    });
  }

  /// بناء تفاصيل الدور
  Widget _buildRoleDetails(Permission role) {
    return PermissionRoleCardWidget(
      role: role,
      isDetailed: true,
      onEdit: () => CreateRoleDialog.show(context, _controller, role: role),
      onDelete: () async {
        final confirm = await showDialog<bool>(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف الدور: ${role.name}؟'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
              ElevatedButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
            ],
          ),
        );
        if (confirm == true) {
          await _controller.deletePermission(role.id);
        }
      },
    );
  }

  /// بناء شبكة الصلاحيات
  Widget _buildPermissionsGrid() {
    // تجميع الصلاحيات حسب الفئة
    final categorizedScopes = _controller.permissionsByGroup;

    return ListView.builder(
      itemCount: categorizedScopes.length,
      itemBuilder: (context, index) {
        final category = categorizedScopes.keys.elementAt(index);
        final permissions = categorizedScopes[category]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            children: permissions.map((permission) => _buildPermissionItem(permission)).toList(),
          ),
        );
      },
    );
  }

  /// بناء عنصر الصلاحية
  Widget _buildPermissionItem(Permission permission) {
    return ListTile(
      title: Text(permission.name),
      subtitle: Text('المجال: ${permission.permissionGroup}'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4),
            child: Tooltip(
              message: "Toggle",
              child: Obx(() => Checkbox(
                value: _controller.customRolePermissions.any((p) => p.id == permission.id),
                onChanged: (value) {
                  if (_controller.currentPermission != null) {
                    if (value == true) {
                      _controller.grantPermissionToCustomRole(_controller.currentPermission!.id, permission.id);
                    } else {
                      _controller.revokePermissionFromCustomRole(_controller.currentPermission!.id, permission.id);
                    }
                  }
                },
              )),
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب صلاحيات الواجهات
  Widget _buildInterfacePermissionsTab() {
    return const Center(
      child: Text(
        'تبويب صلاحيات الواجهات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// تبويب صلاحيات المستخدمين
  Widget _buildUserPermissionsTab() {
    final adminController = Get.find<AdminController>();
    final userPermissionsController = Get.put(UserPermissionsController());
    final user = adminController.selectedPermissionUser.value;

    if (user == null) {
      return const Center(
        child: Text(
          'اختر مستخدمًا من قائمة إدارة المستخدمين ثم اضغط على إدارة الصلاحيات',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // تحميل صلاحيات المستخدم عند فتح التبويب
    userPermissionsController.getUserPermissions(user.id);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.blue,
                child: Text(user.name.substring(0, 1)),
              ),
              const SizedBox(width: 16),
              Text(user.name, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              const SizedBox(width: 16),
              Text(user.email, style: const TextStyle(color: Colors.grey)),
            ],
          ),
          const SizedBox(height: 24),
          const Divider(),
          // واجهة تعديل الأدوار والصلاحيات
          Expanded(
            child: Obx(() {
              if (userPermissionsController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }
              final allPermissions = Get.find<NewPermissionsController>().allPermissions;
              final userPermissionIds = userPermissionsController.userPermissions.map((p) => p.permissionId).toSet();
              return ListView(
                children: [
                  ...allPermissions.map((perm) => CheckboxListTile(
                        value: userPermissionIds.contains(perm.id),
                        title: Text(perm.name),
                        subtitle: perm.description != null ? Text(perm.description!) : null,
                        onChanged: (checked) async {
                          if (checked == true) {
                            await userPermissionsController.grantPermission(user.id, perm.id);
                          } else {
                            await userPermissionsController.revokePermission(user.id, perm.id);
                          }
                        },
                      )),
                ],
              );
            }),
          ),
          const SizedBox(height: 16),
          // زر حفظ التغييرات (اختياري إذا أردت حفظ دفعة واحدة)
          // ElevatedButton(
          //   onPressed: () async {
          //     // منطق الحفظ الجماعي هنا
          //   },
          //   child: const Text('حفظ التغييرات'),
          // ),
        ],
      ),
    );
  }

  /// عرض حوار إنشاء دور جديد
  void _showCreateRoleDialog() {
    CreateRoleDialog.show(context, _controller);
  }
}
