import 'package:flutter/foundation.dart';
import '../../models/user_model.dart';
import 'api_service.dart';

/// خدمة API للمستخدمين - متطابقة مع ASP.NET Core API
class UsersApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiService.get('/api/Users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      rethrow;
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم $id: $e');
      return null;
    }
  }

  /// الحصول على المستخدم الحالي
  Future<User?> getCurrentUser() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم الحالي: $e');
      return null;
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين النشطين: $e');
      rethrow;
    }
  }

  /// الحصول على مستخدمي قسم محدد
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/$departmentId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي القسم $departmentId: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين بحسب الدور
  Future<List<User>> getUsersByRole(String role) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/$role');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي الدور $role: $e');
      rethrow;
    }
  }

  /// إنشاء مستخدم جديد
  Future<User> createUser(User user) async {
    try {
      final response = await _apiService.post(
        '/api/Users',
        user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث مستخدم
  Future<User> updateUser(int id, User user) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>',
        user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم $id: $e');
      rethrow;
    }
  }

  /// تحديث الملف الشخصي للمستخدم الحالي
  Future<User> updateCurrentUserProfile(User user) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/profile',
        user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الملف الشخصي: $e');
      rethrow;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int id) async {
    try {
      final response = await _apiService.delete('/api/Users/<USER>');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم $id: $e');
      return false;
    }
  }

  /// تفعيل مستخدم
  Future<bool> activateUser(int id) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/activate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل المستخدم: $e');
      return false;
    }
  }

  /// إلغاء تفعيل مستخدم
  Future<bool> deactivateUser(int id) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/deactivate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل المستخدم: $e');
      return false;
    }
  }

  /// تغيير كلمة المرور
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/change-password',
        {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير كلمة المرور: $e');
      return false;
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<bool> resetPassword(int userId, String newPassword) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/reset-password',
        {
          'newPassword': newPassword,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين كلمة المرور: $e');
      return false;
    }
  }

  /// البحث في المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في المستخدمين: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات المستخدمين
  Future<Map<String, dynamic>> getUserStatistics() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستخدمين: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات مستخدم محدد
  Future<Map<String, dynamic>> getUserStatisticsById(int userId) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستخدم: $e');
      return {};
    }
  }

  /// تحديث صورة المستخدم
  Future<bool> updateUserAvatar(int userId, String avatarUrl) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/avatar',
        {
          'avatarUrl': avatarUrl,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث صورة المستخدم: $e');
      return false;
    }
  }

  /// تحديث إعدادات المستخدم
  Future<bool> updateUserSettings(int userId, Map<String, dynamic> settings) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/settings',
        settings,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات المستخدم: $e');
      return false;
    }
  }

  /// الحصول على إعدادات المستخدم
  Future<Map<String, dynamic>> getUserSettings(int userId) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/settings');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات المستخدم: $e');
      return {};
    }
  }

  /// تحديث آخر نشاط للمستخدم
  Future<bool> updateLastActivity() async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>/last-activity',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث آخر نشاط: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين المتصلين حالياً
  Future<List<User>> getOnlineUsers() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين المتصلين: $e');
      return [];
    }
  }

  /// تصدير بيانات المستخدمين
  Future<Map<String, dynamic>> exportUsers() async {
    try {
      final response = await _apiService.get('/api/Users/<USER>');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير بيانات المستخدمين: $e');
      return {};
    }
  }
}
