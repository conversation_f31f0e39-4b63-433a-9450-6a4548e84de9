// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import '../../models/enhanced_permission_models.dart';
// import '../../models/permission_models.dart';
// import '../../models/user_permission_models.dart';
// import '../../models/user_model.dart';
// import 'base_api_service.dart';

// /// خدمة API لإدارة الأدوار والصلاحيات المحسنة - متوافقة مع ASP.NET Core
// class EnhancedPermissionsApiService extends GetxService {
//   late final BaseApiService _apiService;

//   EnhancedPermissionsApiService() {
//     try {
//       _apiService = Get.find<BaseApiService>();
//     } catch (e) {
//       _apiService = BaseApiService();
//     }
//   }

//   /// الحصول على جميع الصلاحيات
//   Future<List<Permission>> getAllPermissions() async {
//     try {
//       final response = await _apiService.get('/api/Permissions');
//       return _apiService.handleResponse<List<Permission>>(
//         response,
//         (json) => (json as List)
//             .map((item) => Permission.fromJson(item as Map<String, dynamic>))
//             .toList(),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على الصلاحيات: $e');
//       throw Exception('خطأ في الحصول على الصلاحيات: $e');
//     }
//   }

//   /// الحصول على الأدوار المخصصة (من مجموعة CustomRoles)
//   Future<List<CustomRole>> getCustomRoles() async {
//     try {
//       final response = await _apiService.get('/api/Permissions?group=CustomRoles');
//       return _apiService.handleResponse<List<CustomRole>>(
//         response,
//         (json) => (json as List)
//             .map((item) => CustomRole.fromPermission(
//                 Permission.fromJson(item as Map<String, dynamic>)))
//             .toList(),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على الأدوار المخصصة: $e');
//       throw Exception('خطأ في الحصول على الأدوار المخصصة: $e');
//     }
//   }

//   /// إنشاء دور مخصص جديد
//   Future<CustomRole> createCustomRole(CreateRoleRequest request) async {
//     try {
//       debugPrint('إنشاء دور مخصص جديد: ${request.name}');

//       final response = await _apiService.post(
//         '/api/Permissions',
//         request.toJson(),
//       );

//       final permission = _apiService.handleResponse<Permission>(
//         response,
//         (json) => Permission.fromJson(json),
//       );

//       debugPrint('تم إنشاء الدور المخصص بنجاح: ${permission.id}');
//       return CustomRole.fromPermission(permission);
//     } catch (e) {
//       debugPrint('خطأ في إنشاء الدور المخصص: $e');
//       throw Exception('خطأ في إنشاء الدور المخصص: $e');
//     }
//   }

//   /// تحديث دور مخصص
//   Future<CustomRole> updateCustomRole(UpdateRoleRequest request) async {
//     try {
//       debugPrint('تحديث الدور المخصص: ${request.id}');

//       final response = await _apiService.put(
//         '/api/Permissions/${request.id}',
//         request.toJson(),
//       );

//       final permission = _apiService.handleResponse<Permission>(
//         response,
//         (json) => Permission.fromJson(json),
//       );

//       debugPrint('تم تحديث الدور المخصص بنجاح: ${permission.id}');
//       return CustomRole.fromPermission(permission);
//     } catch (e) {
//       debugPrint('خطأ في تحديث الدور المخصص: $e');
//       throw Exception('خطأ في تحديث الدور المخصص: $e');
//     }
//   }

//   /// حذف دور مخصص
//   Future<void> deleteCustomRole(int roleId) async {
//     try {
//       debugPrint('حذف الدور المخصص: $roleId');

//       await _apiService.delete('/api/Permissions/$roleId');

//       debugPrint('تم حذف الدور المخصص بنجاح: $roleId');
//     } catch (e) {
//       debugPrint('خطأ في حذف الدور المخصص: $e');
//       throw Exception('خطأ في حذف الدور المخصص: $e');
//     }
//   }

//   /// الحصول على صلاحيات المستخدم
//   Future<List<UserPermission>> getUserPermissions(int userId) async {
//     try {
//       final response = await _apiService.get('/api/Permissions/user/$userId');
//       return _apiService.handleResponse<List<UserPermission>>(
//         response,
//         (json) => (json as List)
//             .map((item) => UserPermission.fromJson(item as Map<String, dynamic>))
//             .toList(),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على صلاحيات المستخدم: $e');
//       throw Exception('خطأ في الحصول على صلاحيات المستخدم: $e');
//     }
//   }

//   /// منح صلاحية للمستخدم
//   Future<UserPermission> grantPermissionToUser({
//     required int userId,
//     required int permissionId,
//     int? expiresAt,
//   }) async {
//     try {
//       debugPrint('منح صلاحية للمستخدم: $userId -> $permissionId');

//       final response = await _apiService.post(
//         '/api/Permissions/grant',
//         {
//           'userId': userId,
//           'permissionId': permissionId,
//           'expiresAt': expiresAt,
//         },
//       );

//       final userPermission = _apiService.handleResponse<UserPermission>(
//         response,
//         (json) => UserPermission.fromJson(json),
//       );

//       debugPrint('تم منح الصلاحية بنجاح: ${userPermission.id}');
//       return userPermission;
//     } catch (e) {
//       debugPrint('خطأ في منح الصلاحية: $e');
//       throw Exception('خطأ في منح الصلاحية: $e');
//     }
//   }

//   /// إلغاء صلاحية من المستخدم
//   Future<void> revokePermissionFromUser({
//     required int userId,
//     required int permissionId,
//   }) async {
//     try {
//       debugPrint('إلغاء صلاحية من المستخدم: $userId -> $permissionId');

//       await _apiService.delete('/api/Permissions/revoke/$userId/$permissionId');

//       debugPrint('تم إلغاء الصلاحية بنجاح');
//     } catch (e) {
//       debugPrint('خطأ في إلغاء الصلاحية: $e');
//       throw Exception('خطأ في إلغاء الصلاحية: $e');
//     }
//   }

//   /// الحصول على المستخدمين الذين لديهم صلاحية معينة
//   Future<List<User>> getUsersWithPermission(int permissionId) async {
//     try {
//       final response = await _apiService.get('/api/Permissions/$permissionId/users');
//       return _apiService.handleResponse<List<User>>(
//         response,
//         (json) => (json as List)
//             .map((item) => User.fromJson(item as Map<String, dynamic>))
//             .toList(),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المستخدمين: $e');
//       throw Exception('خطأ في الحصول على المستخدمين: $e');
//     }
//   }

//   /// الحصول على مجموعات الصلاحيات
//   Future<List<String>> getPermissionGroups() async {
//     try {
//       final response = await _apiService.get('/api/Permissions/groups');
//       return _apiService.handleResponse<List<String>>(
//         response,
//         (json) => (json as List).cast<String>(),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على مجموعات الصلاحيات: $e');
//       throw Exception('خطأ في الحصول على مجموعات الصلاحيات: $e');
//     }
//   }

//   /// التحقق من صلاحية المستخدم
//   Future<bool> checkUserPermission({
//     required int userId,
//     required int permissionId,
//   }) async {
//     try {
//       final response = await _apiService.get(
//         '/api/Permissions/check/$userId/$permissionId',
//       );
//       return _apiService.handleResponse<bool>(
//         response,
//         (json) => json['hasPermission'] ?? false,
//       );
//     } catch (e) {
//       debugPrint('خطأ في التحقق من الصلاحية: $e');
//       return false;
//     }
//   }

//   /// تحديث صلاحيات متعددة للمستخدم
//   Future<void> updateUserPermissions({
//     required int userId,
//     required List<int> permissionIds,
//   }) async {
//     try {
//       debugPrint('تحديث صلاحيات المستخدم: $userId');

//       await _apiService.post(
//         '/api/Permissions/update-user-permissions',
//         {
//           'userId': userId,
//           'permissionIds': permissionIds,
//         },
//       );

//       debugPrint('تم تحديث صلاحيات المستخدم بنجاح');
//     } catch (e) {
//       debugPrint('خطأ في تحديث صلاحيات المستخدم: $e');
//       throw Exception('خطأ في تحديث صلاحيات المستخدم: $e');
//     }
//   }

//   /// تهيئة الخدمة
//   Future<void> initialize() async {
//     await _apiService.initialize();
//   }
// }
