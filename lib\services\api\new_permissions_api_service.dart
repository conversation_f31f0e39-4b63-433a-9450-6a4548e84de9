
// --- a/lib/services/api/new_permissions_api_service.dart
// +++ b/lib/services/api/new_permissions_api_service.dart
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/permission_models.dart';

import 'api_service.dart';

/// خدمة API الجديدة للصلاحيات الـ 71 - متوافقة مع الباك إند الجديد
class NewPermissionsApiService {
  final ApiService _apiService = ApiService();

  // ===== الصلاحيات الأساسية =====

  /// الحصول على جميع الصلاحيات الـ 71
  Future<List<Permission>> getAllPermissions() async {
    try {
      debugPrint('🔄 جلب جميع الصلاحيات الـ 71...');
      final response = await _apiService.get('/api/Permissions');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الصلاحيات: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحية بالمعرف مع العلاقات
  Future<Permission?> getPermissionById(int id, {bool includeRelations = true}) async {
    try {
      debugPrint('🔄 جلب الصلاحية $id...');
      final endpoint = includeRelations 
          ? '/api/Permissions/$id?include=screen,action,userPermissions'
          : '/api/Permissions/$id';
      
      final response = await _apiService.get(endpoint);
      final permission = _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب الصلاحية: ${permission.name}');
      return permission;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الصلاحية $id: $e');
      return null;
    }
  }

  /// الحصول على الصلاحيات بحسب المجموعة
  Future<List<Permission>> getPermissionsByGroup(String group) async {
    try {
      debugPrint('🔄 جلب صلاحيات المجموعة: $group');
      final response = await _apiService.get('/api/Permissions/group/$group');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للمجموعة $group');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات المجموعة $group: $e');
      rethrow;
    }
  }

  /// الحصول على الصلاحيات بحسب الشاشة
  Future<List<Permission>> getPermissionsByScreen(int screenId) async {
    try {
      debugPrint('🔄 جلب صلاحيات الشاشة: $screenId');
      final response = await _apiService.get('/api/Permissions/screen/$screenId');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للشاشة $screenId');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات الشاشة $screenId: $e');
      return [];
    }
  }

  /// الحصول على الصلاحيات بحسب العملية
  Future<List<Permission>> getPermissionsByAction(int actionId) async {
    try {
      debugPrint('🔄 جلب صلاحيات العملية: $actionId');
      final response = await _apiService.get('/api/Permissions/action/$actionId');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للعملية $actionId');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات العملية $actionId: $e');
      return [];
    }
  }

  // ===== إدارة الصلاحيات =====

  /// إنشاء صلاحية جديدة
  Future<Permission> createPermission(Permission permission) async {
    try {
      debugPrint('🔄 إنشاء صلاحية جديدة: ${permission.name}');
      final response = await _apiService.post(
        '/api/Permissions',
        permission.toJson(),
      );
      final newPermission = _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم إنشاء الصلاحية: ${newPermission.name}');
      return newPermission;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الصلاحية: $e');
      rethrow;
    }
  }

  /// تحديث صلاحية
  Future<Permission> updatePermission(int id, Permission permission) async {
    try {
      debugPrint('🔄 تحديث الصلاحية $id: ${permission.name}');
      final response = await _apiService.put(
        '/api/Permissions/$id',
        permission.toJson(),
      );
      final updatedPermission = _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم تحديث الصلاحية: ${updatedPermission.name}');
      return updatedPermission;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الصلاحية $id: $e');
      rethrow;
    }
  }

  /// حذف صلاحية
  Future<bool> deletePermission(int id) async {
    try {
      debugPrint('🔄 حذف الصلاحية $id');
      final response = await _apiService.delete('/api/Permissions/$id');
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم حذف الصلاحية $id');
      } else {
        debugPrint('❌ فشل في حذف الصلاحية $id');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الصلاحية $id: $e');
      return false;
    }
  }

  // ===== التحقق من الصلاحيات =====

  /// التحقق من صلاحية مستخدم محدد
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية $permissionName للمستخدم $userId');
      final response = await _apiService.get('/api/Permissions/check/$userId/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      final hasPermission = data['hasPermission'] as bool? ?? false;
      debugPrint('✅ نتيجة التحقق: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// التحقق من صلاحية المستخدم الحالي
  Future<bool> checkCurrentUserPermission(String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية المستخدم الحالي: $permissionName');
      final response = await _apiService.get('/api/Permissions/check-current/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      final hasPermission = data['hasPermission'] as bool? ?? false;
      debugPrint('✅ نتيجة التحقق: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// التحقق من صلاحيات متعددة دفعة واحدة
  Future<Map<String, bool>> checkMultiplePermissions(int userId, List<String> permissionNames) async {
    try {
      debugPrint('🔍 التحقق من ${permissionNames.length} صلاحية للمستخدم $userId');
      final response = await _apiService.post(
        '/api/Permissions/check-multiple/$userId',
        {'permissions': permissionNames},
      );
      
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      
      final results = <String, bool>{};
      final resultsList = data['results'] as List<dynamic>? ?? [];
      
      for (final result in resultsList) {
        if (result is Map<String, dynamic>) {
          final permission = result['permission'] as String?;
          final hasPermission = result['hasPermission'] as bool?;
          
          if (permission != null && hasPermission != null) {
            results[permission] = hasPermission;
          }
        }
      }
      
      debugPrint('✅ تم التحقق من ${results.length} صلاحية');
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
      return {};
    }
  }

  // ===== إدارة صلاحيات المستخدمين =====

  /// منح صلاحية لمستخدم
  Future<bool> grantPermissionToUser(int userId, int permissionId) async {
    try {
      debugPrint('🔄 منح الصلاحية $permissionId للمستخدم $userId');
      final response = await _apiService.post(
        '/api/Permissions/grant-to-user',
        {
          'userId': userId,
          'permissionId': permissionId,
        },
      );
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم منح الصلاحية بنجاح');
      } else {
        debugPrint('❌ فشل في منح الصلاحية');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في منح الصلاحية: $e');
      return false;
    }
  }

  /// إلغاء صلاحية من مستخدم
  Future<bool> revokePermissionFromUser(int userId, int permissionId) async {
    try {
      debugPrint('🔄 إلغاء الصلاحية $permissionId من المستخدم $userId');
      final response = await _apiService.post(
        '/api/Permissions/revoke-from-user',
        {
          'userId': userId,
          'permissionId': permissionId,
        },
      );
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم إلغاء الصلاحية بنجاح');
      } else {
        debugPrint('❌ فشل في إلغاء الصلاحية');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الصلاحية: $e');
      return false;
    }
  }

  /// الحصول على جميع صلاحيات المستخدم
  Future<List<Permission>> getUserPermissions(int userId) async {
    try {
      debugPrint('🔄 جلب صلاحيات المستخدم $userId');
      final response = await _apiService.get('/api/Permissions/user/$userId');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للمستخدم');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات المستخدم: $e');
      return [];
    }
  }

  // ===== إدارة صلاحيات الأدوار المخصصة =====

  /// منح صلاحية لدور مخصص
  Future<bool> grantPermissionToCustomRole(int customRoleId, int permissionId) async {
    try {
      debugPrint('🔄 منح الصلاحية $permissionId للدور المخصص $customRoleId');
      final response = await _apiService.post(
        '/api/Permissions/grant-to-custom-role',
        {
          'customRoleId': customRoleId,
          'permissionId': permissionId,
        },
      );
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم منح الصلاحية للدور المخصص بنجاح');
      } else {
        debugPrint('❌ فشل في منح الصلاحية للدور المخصص');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في منح الصلاحية للدور المخصص: $e');
      return false;
    }
  }

  /// إلغاء صلاحية من دور مخصص
  Future<bool> revokePermissionFromCustomRole(int customRoleId, int permissionId) async {
    try {
      debugPrint('🔄 إلغاء الصلاحية $permissionId من الدور المخصص $customRoleId');
      final response = await _apiService.post(
        '/api/Permissions/revoke-from-custom-role',
        {
          'customRoleId': customRoleId,
          'permissionId': permissionId,
        },
      );
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم إلغاء الصلاحية من الدور المخصص بنجاح');
      } else {
        debugPrint('❌ فشل في إلغاء الصلاحية من الدور المخصص');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الصلاحية من الدور المخصص: $e');
      return false;
    }
  }

  /// الحصول على صلاحيات دور مخصص
  Future<List<Permission>> getCustomRolePermissions(int customRoleId) async {
    try {
      debugPrint('🔄 جلب صلاحيات الدور المخصص $customRoleId');
      final response = await _apiService.get('/api/Permissions/custom-role/$customRoleId');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للدور المخصص');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات الدور المخصص: $e');
      return [];
    }
  }

  // ===== البحث والتصفية =====

  /// البحث في الصلاحيات
  Future<List<Permission>> searchPermissions(String query, {
    String? group,
    int? level,
    bool? isDefault,
  }) async {
    try {
      debugPrint('🔍 البحث في الصلاحيات: $query');
      
      final queryParams = <String, String>{
        'q': query,
        if (group != null) 'group': group,
        if (level != null) 'level': level.toString(),
        if (isDefault != null) 'isDefault': isDefault.toString(),
      };
      
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      
      final response = await _apiService.get('/api/Permissions/search?$queryString');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم العثور على ${permissions.length} صلاحية');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في البحث في الصلاحيات: $e');
      return [];
    }
  }

  /// الحصول على مجموعات الصلاحيات
  Future<List<String>> getPermissionGroups() async {
    try {
      debugPrint('🔄 جلب مجموعات الصلاحيات');
      final response = await _apiService.get('/api/Permissions/groups');
      final groups = _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
      debugPrint('✅ تم جلب ${groups.length} مجموعة');
      return groups;
    } catch (e) {
      debugPrint('❌ خطأ في جلب مجموعات الصلاحيات: $e');
      return [];
    }
  }

  // ===== الإحصائيات والتقارير =====

  /// الحصول على إحصائيات الصلاحيات
  Future<Map<String, dynamic>> getPermissionsStats() async {
    try {
      debugPrint('📊 جلب إحصائيات الصلاحيات');
      final response = await _apiService.get('/api/Permissions/stats');
      final stats = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم جلب الإحصائيات');
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
      return {};
    }
  }

  /// تصدير الصلاحيات
  Future<Map<String, dynamic>> exportPermissions({String format = 'json'}) async {
    try {
      debugPrint('📤 تصدير الصلاحيات بصيغة $format');
      final response = await _apiService.get('/api/Permissions/export?format=$format');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم تصدير الصلاحيات');
      return data;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير الصلاحيات: $e');
      return {};
    }
  }

  // ===== التخزين المؤقت =====

  /// مسح التخزين المؤقت للصلاحيات
  Future<bool> clearPermissionsCache() async {
    try {
      debugPrint('🗑️ مسح التخزين المؤقت للصلاحيات');
      final response = await _apiService.post('/api/Permissions/clear-cache', {});
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم مسح التخزين المؤقت بنجاح');
      } else {
        debugPrint('❌ فشل في مسح التخزين المؤقت');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في مسح التخزين المؤقت: $e');
      return false;
    }
  }

  /// إعادة تحميل التخزين المؤقت للصلاحيات
  Future<bool> reloadPermissionsCache() async {
    try {
      debugPrint('🔄 إعادة تحميل التخزين المؤقت للصلاحيات');
      final response = await _apiService.post('/api/Permissions/reload-cache', {});
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم إعادة تحميل التخزين المؤقت بنجاح');
      } else {
        debugPrint('❌ فشل في إعادة تحميل التخزين المؤقت');
      }
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل التخزين المؤقت: $e');
      return false;
    }
  }
}