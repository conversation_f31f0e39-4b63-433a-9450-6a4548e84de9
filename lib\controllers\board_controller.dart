import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../models/board_model.dart';
import 'auth_controller.dart';

/// متحكم اللوحات
class BoardController extends GetxController {
  // قائمة اللوحات
  final RxList<Board> _boards = <Board>[].obs;
  
  // اللوحة الحالية
  final Rx<Board?> _currentBoard = Rx<Board?>(null);
  
  // حالة التحميل
  final RxBool _isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString _error = ''.obs;

  // Getters
  List<Board> get boards => _boards;
  Board? get currentBoard => _currentBoard.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;

  // Observable getters
  RxList<Board> get boardsObs => _boards;
  Rx<Board?> get currentBoardObs => _currentBoard;

  @override
  void onInit() {
    super.onInit();
    loadBoards();
  }

  /// تحميل اللوحات
  Future<void> loadBoards() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاكاة تحميل اللوحات - في التطبيق الحقيقي ستأتي من API
      await Future.delayed(const Duration(milliseconds: 500));
      
      // تحميل اللوحات من API
      final boards = await _apiService.getAllBoards();
      _boards.assignAll(boards);
      
      if (_boards.isNotEmpty && _currentBoard.value == null) {
        _currentBoard.value = _boards.first;
      }

      debugPrint('تم تحميل ${_boards.length} لوحة');
    } catch (e) {
      _error.value = 'خطأ في تحميل اللوحات: $e';
      debugPrint('خطأ في تحميل اللوحات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء لوحة جديدة
  Future<Board?> createBoard({
    required String name,
    required String description,
    String? color,
    String? icon,
    bool isPublic = false,
  }) async {
    try {
      _isLoading.value = true;
      
      final board = Board(
        id: DateTime.now().millisecondsSinceEpoch,
        name: name,
        description: description,
        ownerId: Get.find<AuthController>().currentUser.value?.id ?? 1, // الحصول من المستخدم الحالي
        createdAt: DateTime.now().millisecondsSinceEpoch,
        isActive: true,
        isPublic: isPublic,
        color: color,
        icon: icon,
      );

      _boards.add(board);
      debugPrint('تم إنشاء لوحة جديدة: $name');
      return board;
    } catch (e) {
      _error.value = 'خطأ في إنشاء اللوحة: $e';
      debugPrint('خطأ في إنشاء اللوحة: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث لوحة
  Future<bool> updateBoard(Board board) async {
    try {
      _isLoading.value = true;
      
      final index = _boards.indexWhere((b) => b.id == board.id);
      if (index != -1) {
        final updatedBoard = board.copyWith(
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );
        _boards[index] = updatedBoard;
        
        if (_currentBoard.value?.id == board.id) {
          _currentBoard.value = updatedBoard;
        }
        
        debugPrint('تم تحديث اللوحة: ${board.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث اللوحة: $e';
      debugPrint('خطأ في تحديث اللوحة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف لوحة
  Future<bool> deleteBoard(int boardId) async {
    try {
      _isLoading.value = true;

      final initialLength = _boards.length;
      _boards.removeWhere((b) => b.id == boardId);
      final wasRemoved = _boards.length < initialLength;

      if (_currentBoard.value?.id == boardId) {
        _currentBoard.value = _boards.isNotEmpty ? _boards.first : null;
      }

      debugPrint('تم حذف اللوحة: $boardId');
      return wasRemoved;
    } catch (e) {
      _error.value = 'خطأ في حذف اللوحة: $e';
      debugPrint('خطأ في حذف اللوحة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تعيين اللوحة الحالية
  void setCurrentBoard(int boardId) {
    final board = _boards.firstWhereOrNull((b) => b.id == boardId);
    if (board != null) {
      _currentBoard.value = board;
      debugPrint('تم تعيين اللوحة الحالية: ${board.name}');
    }
  }

  /// البحث في اللوحات
  List<Board> searchBoards(String query) {
    if (query.isEmpty) return _boards;
    
    return _boards.where((board) =>
      board.name.toLowerCase().contains(query.toLowerCase()) ||
      board.description.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  /// الحصول على اللوحات العامة
  List<Board> get publicBoards => _boards.where((b) => b.isPublic).toList();

  /// الحصول على اللوحات الخاصة
  List<Board> get privateBoards => _boards.where((b) => !b.isPublic).toList();

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// إعادة تحميل اللوحات
  @override
  Future<void> refresh() async {
    await loadBoards();
  }
}
