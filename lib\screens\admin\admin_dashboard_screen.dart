import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/admin_controller.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../models/user_model.dart';

import 'user_management_tab.dart';
import 'backup_restore_tab.dart';
import 'system_settings_tab.dart';
import 'analytics_tab.dart';

import 'database_management_tab.dart';
import 'role_management_tab.dart';
import 'interface_permissions_tab.dart';
import 'enhanced_permissions_management_screen.dart';

import '../../routes/app_routes.dart';
import 'user_permissions_viewer_screen.dart';
import 'task_type_management_tab.dart';
import 'permission_test_screen.dart';

/// شاشة لوحة التحكم الإدارية
///
/// توفر واجهة للمسؤول للتحكم في جميع جوانب النظام
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen>
    with SingleTickerProviderStateMixin, RouteAware {
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final ThemeController _themeController = Get.find<ThemeController>();
  final admincontroller = Get.find<AdminController>();
  late NewPermissionsController _permissionsController;

  // المستخدم المحدد لإدارة الصلاحيات
  User? selectedPermissionUser;

  @override
  void initState() {
    super.initState();

    // التحقق من وجود المتحكم أو إنشاؤه
    if (!Get.isRegistered<NewPermissionsController>()) {
      Get.put(NewPermissionsController());
    }
    _permissionsController = Get.find<NewPermissionsController>();

    // إنشاء متحكم التبويبات
    _tabController = TabController(length: 13, vsync: this);

    // تسجيل متحكم التبويبات في GetX
    Get.put(_tabController, tag: 'admin_tab_controller');

    // الحصول على مراقب المسارات من GetX
    _routeObserver = Get.find<RouteObserver<PageRoute>>();

    // تهيئة البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);
    _tabController.dispose();
    super.dispose();
  }

  /// يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى لوحة التحكم الإدارية');
    // إعادة تحميل البيانات عند العودة إلى الصفحة
    _initializeData();
  }

  /// الانتقال إلى تبويب إدارة الصلاحيات
  void selectPermissionsTab(User user) {
    setState(() {
      selectedPermissionUser = user;
      _tabController.animateTo(1); // الانتقال إلى تبويب الصلاحيات (الثاني)
    });
  }

  /// الانتقال إلى تبويب إدارة الصلاحيات للمستخدم المحدد
  void goToUserPermissionsTab(User user) {
    setState(() {
      selectedPermissionUser = user;
      // الانتقال إلى تبويب "صلاحيات المستخدم" (index 10)
      _tabController.animateTo(10);
    });
  }

  /// تهيئة البيانات الأولية
  Future<void> _initializeData() async {
    // التحقق من صلاحيات المستخدم - السماح للمديرين ومديري النظام
    final hasAccess = await _permissionsController.checkCurrentUserPermission('admin_dashboard_access');
    if (!hasAccess) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'ليس لديك صلاحية للوصول إلى لوحة التحكم الإدارية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }

    try {
      debugPrint('🚀 بدء تهيئة بيانات الداش بورد الإداري...');

      // تحميل البيانات الأساسية أولاً للتحميل السريع
      await _permissionsController.loadAllPermissions();

      // التحقق من نجاح تحميل البيانات الأساسية
      if (_permissionsController.allPermissions.isNotEmpty) {
        debugPrint('✅ تم تحميل البيانات الأساسية بنجاح');

        // ثم تحميل باقي البيانات في الخلفية
        _loadAdditionalData();
      } else if (_permissionsController.error.isNotEmpty) {
        debugPrint('❌ فشل في تحميل البيانات الأساسية');
        _showRetryDialog();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة البيانات: $e');
      _showRetryDialog();
    }
  }

  /// تحميل البيانات الإضافية في الخلفية
  Future<void> _loadAdditionalData() async {
    try {
      debugPrint('📊 بدء تحميل البيانات الإضافية...');
      await Future.wait([
        _permissionsController.loadPermissionGroups(),
        _permissionsController.loadPermissionsStats(),
      ]);
      debugPrint('✅ تم تحميل البيانات الإضافية بنجاح');
    } catch (e) {
      debugPrint('⚠️ خطأ في تحميل البيانات الإضافية: $e');
      // لا نعرض رسالة خطأ للبيانات الإضافية لأنها ليست ضرورية للعمل الأساسي
    }
  }

  /// عرض حوار إعادة المحاولة
  void _showRetryDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ في تحميل البيانات'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _permissionsController.error.isNotEmpty
                ? _permissionsController.error
                : 'حدث خطأ أثناء تحميل بيانات الداش بورد',
            ),
            const SizedBox(height: 16),
            const Text(
              'هل تريد المحاولة مرة أخرى؟',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // إغلاق الحوار
              Get.back(); // العودة للشاشة السابقة
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // إغلاق الحوار
              await _retryInitialization();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// إعادة محاولة التهيئة
  Future<void> _retryInitialization() async {
    debugPrint('🔄 إعادة محاولة تهيئة الداش بورد...');
    await _permissionsController.refresh();

    if (_permissionsController.allPermissions.isNotEmpty) {
      debugPrint('✅ نجحت إعادة المحاولة');
      _loadAdditionalData();
      Get.snackbar(
        'تم بنجاح',
        'تم تحميل البيانات بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } else {
      debugPrint('❌ فشلت إعادة المحاولة');
      _showRetryDialog(); // عرض الحوار مرة أخرى
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام متغيرات مراقبة مباشرة
    final isArabic = Get.locale?.languageCode == 'ar';
    final isDarkMode = _themeController.isDarkMode;

    // التحقق مما إذا كانت الشاشة مضمنة داخل HomeScreen
    final isEmbedded = ModalRoute.of(context)?.settings.name != '/admin';

    // استخدام GetBuilder بدلاً من Obx لتجنب التحديثات المتكررة
    return GetBuilder<NewPermissionsController>(
      builder: (_) {
        return Directionality(
          textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
          child: Scaffold(
            // إظهار AppBar فقط إذا لم تكن الشاشة مضمنة داخل HomeScreen
            appBar: isEmbedded
                ? null
                : AppBar(
                    title: Text('adminDashboard'.tr),
                    centerTitle: true,
                    elevation: 2,
                    actions: [
                      // زر تحديث البيانات
                      Obx(() => IconButton(
                        icon: _permissionsController.isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.refresh),
                        tooltip: 'تحديث البيانات',
                        onPressed: _permissionsController.isLoading
                            ? null
                            : () async {
                                await _permissionsController.refresh();
                                Get.snackbar(
                                  'تم التحديث',
                                  'تم تحديث جميع البيانات بنجاح',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                  duration: const Duration(seconds: 2),
                                );
                              },
                      )),
                      // زر اختبار نظام الصلاحيات
                      IconButton(
                        icon: const Icon(Icons.security_outlined),
                        tooltip: 'اختبار نظام الصلاحيات',
                        onPressed: () {
                          Get.toNamed(AppRoutes.permissionTest);
                        },
                      ),
                      // زر تبديل الوضع الليلي (Dark Mode)
                      IconButton(
                        icon: Icon(
                          isDarkMode ? Icons.light_mode : Icons.dark_mode,
                        ),
                        tooltip: isDarkMode ? 'lightMode'.tr : 'darkMode'.tr,
                        onPressed: () {
                          _themeController.toggleTheme();
                        },
                      ),
                    ],
                    bottom: _buildTabBar(),
                  ),
            body: Column(
              children: [
                // إضافة شريط التبويبات إذا كانت الشاشة مضمنة داخل HomeScreen
                if (isEmbedded) _buildTabBar(),

                // محتوى الشاشة
                Expanded(child: _buildBody()),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء شريط التبويبات
  PreferredSizeWidget _buildTabBar() {
    // التحقق مما إذا كانت الشاشة مضمنة داخل HomeScreen
    final isEmbedded = ModalRoute.of(context)?.settings.name != '/admin';
    final isDarkMode = _themeController.isDarkMode;

    return TabBar(
      controller: _tabController,
      isScrollable: true,
      // استخدام ألوان مختلفة بناءً على ما إذا كانت الشاشة مضمنة أم لا
      labelColor:
          isEmbedded ? (isDarkMode ? Colors.white : Colors.blue) : Colors.white,
      unselectedLabelColor: isEmbedded
          ? (isDarkMode ? Colors.grey[300] : Colors.grey[700])
          : Colors.white70,
      indicatorColor:
          isEmbedded ? (isDarkMode ? Colors.white : Colors.blue) : Colors.white,
      tabs: const [
        Tab(icon: Icon(Icons.people), text: 'إدارة المستخدمين'),
        Tab(icon: Icon(Icons.security), text: 'إدارة الصلاحيات'),
        Tab(icon: Icon(Icons.admin_panel_settings), text: 'إدارة الأدوار'),
        Tab(icon: Icon(Icons.web), text: 'صلاحيات الواجهات'),
        Tab(icon: Icon(Icons.enhanced_encryption), text: 'الصلاحيات المحسنة'),
        Tab(icon: Icon(Icons.backup), text: 'النسخ الاحتياطية'),
        Tab(icon: Icon(Icons.settings), text: 'إعدادات النظام'),
        Tab(icon: Icon(Icons.analytics), text: 'التحليلات'),
        Tab(icon: Icon(Icons.storage), text: 'إدارة قاعدة البيانات'),
        Tab(icon: Icon(Icons.verified_user), text: 'صلاحيات المستخدم'),

        Tab(icon: Icon(Icons.category), text: 'أنواع المهام'),
        Tab(icon: Icon(Icons.bug_report), text: 'اختبار الصلاحيات'),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        UserManagementTab(
          onGoToUserPermissions: goToUserPermissionsTab,
        ),
        const EnhancedPermissionsManagementScreen(isTab: true),
        const RoleManagementTab(),
        const InterfacePermissionsTab(),
        const EnhancedPermissionsManagementScreen(isTab: true),
        const BackupRestoreTab(),
        const SystemSettingsTab(),
        const AnalyticsTab(),
        const DatabaseManagementTab(),
        const UserPermissionsViewerScreen(),
        const TaskTypeManagementTab(),
        const PermissionTestScreen(),
      ],
    );
  }
}
