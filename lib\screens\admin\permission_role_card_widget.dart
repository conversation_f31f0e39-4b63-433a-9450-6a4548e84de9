import 'package:flutter/material.dart';
import '../../models/permission_models.dart';

class PermissionRoleCardWidget extends StatelessWidget {
  final Permission role;
  final bool isDetailed;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onTap;
  final bool isSelected;

  const PermissionRoleCardWidget({
    super.key,
    required this.role,
    this.isDetailed = false,
    this.onEdit,
    this.onDelete,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = role.color != null
        ? Color(int.parse(role.color!.replaceFirst('#', '0xFF')))
        : Theme.of(context).primaryColor;
    final icon = role.icon != null
        ? IconData(int.parse(role.icon!), fontFamily: 'MaterialIcons')
        : Icons.group;

    if (isDetailed) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: color,
                    child: Icon(icon, color: Colors.white, size: 30),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          role.name,
                          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                        if (role.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            role.description!,
                            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (onEdit != null || onDelete != null)
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit' && onEdit != null) onEdit!();
                        if (value == 'delete' && onDelete != null) onDelete!();
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          const PopupMenuItem(value: 'edit', child: ListTile(leading: Icon(Icons.edit), title: Text('تعديل'))),
                        if (onDelete != null)
                          const PopupMenuItem(value: 'delete', child: ListTile(leading: Icon(Icons.delete), title: Text('حذف'))),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      );
    } else {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withAlpha(25) : null,
          borderRadius: BorderRadius.circular(8),
          border: isSelected ? Border.all(color: Theme.of(context).primaryColor, width: 2) : null,
        ),
        child: ListTile(
          onTap: onTap,
          leading: CircleAvatar(
            backgroundColor: color,
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          title: Text(
            role.name,
            style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
          ),
          subtitle: role.description != null
              ? Text(role.description!, maxLines: 2, overflow: TextOverflow.ellipsis)
              : null,
          trailing: PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit' && onEdit != null) onEdit!();
              if (value == 'delete' && onDelete != null) onDelete!();
            },
            itemBuilder: (context) => [
              if (onEdit != null)
                const PopupMenuItem(value: 'edit', child: ListTile(leading: Icon(Icons.edit), title: Text('تعديل'))),
              if (onDelete != null)
                const PopupMenuItem(value: 'delete', child: ListTile(leading: Icon(Icons.delete), title: Text('حذف'))),
            ],
          ),
        ),
      );
    }
  }
}
