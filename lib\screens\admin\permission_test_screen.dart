import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/permission_test.dart';
import '../../controllers/auth_controller.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';

/// شاشة اختبار نظام الصلاحيات
///
/// تستخدم هذه الشاشة لاختبار نظام الصلاحيات الموحد
/// وتوحيد نظام الصلاحيات ونظام الوصول للمهام
class PermissionTestScreen extends StatefulWidget {
  const PermissionTestScreen({super.key});

  @override
  State<PermissionTestScreen> createState() => _PermissionTestScreenState();
}

class _PermissionTestScreenState extends State<PermissionTestScreen> {
  final PermissionTest _permissionTest = PermissionTest();
  final AuthController _authController = Get.find<AuthController>();

  
  List<Task> _tasks = [];
  List<User> _users = [];
  
  Task? _selectedTask;
  User? _selectedUser;
  
  bool _isLoading = true;
  String _testResults = '';
  
  @override
  void initState() {
    super.initState();
    _loadData();
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // تحميل المهام
      final tasks = <Task>[];

      // تحميل المستخدمين
      final users = <User>[];

      setState(() {
        _tasks = tasks;
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _testResults = 'خطأ في تحميل البيانات: $e';
      });
    }
  }
  
  Future<void> _runTest() async {
    if (_selectedTask == null || _selectedUser == null) {
      setState(() {
        _testResults = 'يرجى اختيار مهمة ومستخدم';
      });
      return;
    }
    
    setState(() {
      _testResults = 'جاري تنفيذ الاختبار...';
    });
    
    try {
      // تنفيذ الاختبار
      final testOutput = StringBuffer();
      
      // تسجيل نتائج الاختبار
      final originalPrint = debugPrint;
      debugPrint = (String? message, {int? wrapWidth}) {
        originalPrint(message, wrapWidth: wrapWidth);
        if (message != null) {
          testOutput.writeln(message);
        }
      };
      
      // تشغيل الاختبار
      await _permissionTest.runAllTests(_selectedTask!.id.toString(), _selectedUser!.id.toString());
      
      // استعادة دالة الطباعة الأصلية
      debugPrint = originalPrint;
      
      setState(() {
        _testResults = testOutput.toString();
      });
    } catch (e) {
      setState(() {
        _testResults = 'خطأ في تنفيذ الاختبار: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نظام الصلاحيات الموحد'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم الحالي
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'المستخدم الحالي',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'الاسم: ${_authController.currentUser.value?.name ?? "غير معروف"}',
                          ),
                          Text(
                            'الدور: ${_authController.currentUser.value?.role.toString() ?? "غير معروف"}',
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // اختيار المهمة
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'اختيار المهمة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DropdownButton<Task>(
                            isExpanded: true,
                            hint: const Text('اختر مهمة'),
                            value: _selectedTask,
                            onChanged: (Task? newValue) {
                              setState(() {
                                _selectedTask = newValue;
                              });
                            },
                            items: _tasks.map<DropdownMenuItem<Task>>((Task task) {
                              return DropdownMenuItem<Task>(
                                value: task,
                                child: Text(task.title),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // اختيار المستخدم
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'اختيار المستخدم',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DropdownButton<User>(
                            isExpanded: true,
                            hint: const Text('اختر مستخدم'),
                            value: _selectedUser,
                            onChanged: (User? newValue) {
                              setState(() {
                                _selectedUser = newValue;
                              });
                            },
                            items: _users.map<DropdownMenuItem<User>>((User user) {
                              return DropdownMenuItem<User>(
                                value: user,
                                child: Text(user.name),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // زر تنفيذ الاختبار
                  Center(
                    child: ElevatedButton(
                      onPressed: _runTest,
                      child: const Text('تنفيذ الاختبار'),
                    ),
                  ),
                  
                  // نتائج الاختبار
                  if (_testResults.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'نتائج الاختبار',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SelectableText(_testResults),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
