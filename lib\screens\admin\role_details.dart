import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/new_permissions_controller.dart';
import 'package:flutter_application_2/models/custom_role_model.dart';
import 'package:get/get.dart';

import '../../models/permission_models.dart';

class RoleDetails extends StatelessWidget {
  final CustomRole role;

  const RoleDetails({super.key, required this.role});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NewPermissionsController>();
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الدور
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Theme.of(context).primaryColor,
                child: Icon(
                  Icons.group,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      role.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (role.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        role.description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Chip(
                          label: Text('المستوى: ${role.id}'),
                          backgroundColor: Theme.of(context).primaryColor.withAlpha(25),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),
          const Divider(),

          // صلاحيات الدور
          const Text(
            'صلاحيات الدور',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: _buildPermissionsGrid(controller),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة الصلاحيات
  Widget _buildPermissionsGrid(NewPermissionsController controller) {
    // تجميع الصلاحيات حسب الفئة
    final categorizedScopes = controller.permissionsByGroup;

    return const Center(
      child: Text('عرض الصلاحيات غير مدعوم للأدوار المخصصة في هذا السياق'),
    );
  }

  /// بناء عنصر الصلاحية
  Widget _buildPermissionItem(Permission permission, NewPermissionsController controller) {
    return ListTile(
      title: Text(permission.name),
      subtitle: Text('المجال: ${permission.permissionGroup}'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4),
            child: Tooltip(
              message: "Toggle",
              child: Obx(() => Checkbox(
                value: controller.customRolePermissions.any((p) => p.id == permission.id),
                onChanged: (value) {
                  if (controller.currentPermission != null) {
                    if (value == true) {
                      controller.grantPermissionToCustomRole(controller.currentPermission!.id, permission.id);
                    } else {
                      controller.revokePermissionFromCustomRole(controller.currentPermission!.id, permission.id);
                    }
                  }
                },
              )),
            ),
          ),
        ],
      ),
    );
  }
}