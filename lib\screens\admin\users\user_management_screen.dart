import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/user_model.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import 'user_form_dialog.dart';

/// شاشة إدارة المستخدمين المحسنة
class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxBool _isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    _setupSearch();
  }

  /// تحميل المستخدمين
  Future<void> _loadUsers() async {
    _isLoading.value = true;
    try {
      await _adminController.loadUsers();
      _filterUsers();
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل المستخدمين: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعداد البحث
  void _setupSearch() {
    _searchController.addListener(_filterUsers);
    _filteredUsers.assignAll(_adminController.users);
  }

  /// تصفية المستخدمين
  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      _filteredUsers.assignAll(_adminController.users);
    } else {
      _filteredUsers.assignAll(
        _adminController.users.where((user) =>
          user.name.toLowerCase().contains(query) ||
          user.email.toLowerCase().contains(query) ||
          (user.username?.toLowerCase().contains(query) ?? false)
        ).toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // زر إضافة مستخدم جديد
          if (_permissionService.canCreateUser())
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddUserDialog,
              tooltip: 'إضافة مستخدم جديد',
            ),
          // زر تحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          
          // قائمة المستخدمين
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (_filteredUsers.isEmpty) {
                return _buildEmptyState();
              }
              
              return _buildUsersList();
            }),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في المستخدمين...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _filterUsers();
                },
              )
            : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isNotEmpty
              ? 'لا توجد نتائج للبحث'
              : 'لا يوجد مستخدمين',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty
              ? 'جرب كلمات بحث مختلفة'
              : 'ابدأ بإضافة مستخدم جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          if (_permissionService.canCreateUser()) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showAddUserDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مستخدم جديد'),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUsersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: user.isActive ? Colors.green : Colors.grey,
          child: Text(
            user.name.substring(0, 1).toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          user.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.email),
            if (user.username != null)
              Text('اسم المستخدم: ${user.username}'),
            Row(
              children: [
                Icon(
                  user.isActive ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: user.isActive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  user.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    color: user.isActive ? Colors.green : Colors.red,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleUserAction(value, user),
          itemBuilder: (context) => [
            if (_permissionService.canEditUser())
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('تعديل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            if (_permissionService.canManagePermissions())
              const PopupMenuItem(
                value: 'permissions',
                child: ListTile(
                  leading: Icon(Icons.security),
                  title: Text('الصلاحيات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            PopupMenuItem(
              value: user.isActive ? 'deactivate' : 'activate',
              child: ListTile(
                leading: Icon(
                  user.isActive ? Icons.block : Icons.check_circle,
                  color: user.isActive ? Colors.red : Colors.green,
                ),
                title: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            if (_permissionService.canDeleteUser())
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('حذف', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// معالجة إجراءات المستخدم
  void _handleUserAction(String action, User user) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'permissions':
        // TODO: فتح شاشة إدارة صلاحيات المستخدم
        Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(user);
        break;
      case 'delete':
        _deleteUser(user);
        break;
    }
  }

  /// عرض حوار إضافة مستخدم
  void _showAddUserDialog() {
    Get.dialog(
      const UserFormDialog(),
    ).then((result) {
      if (result == true) {
        _loadUsers();
      }
    });
  }

  /// عرض حوار تعديل مستخدم
  void _showEditUserDialog(User user) {
    Get.dialog(
      UserFormDialog(user: user),
    ).then((result) {
      if (result == true) {
        _loadUsers();
      }
    });
  }

  /// تبديل حالة المستخدم
  Future<void> _toggleUserStatus(User user) async {
    final action = user.isActive ? 'إلغاء تفعيل' : 'تفعيل';
    final confirmed = await AdminConfirmDialog.show(
      title: '$action المستخدم',
      message: 'هل أنت متأكد من $action المستخدم "${user.name}"؟',
      confirmText: action,
      icon: user.isActive ? Icons.block : Icons.check_circle,
      confirmColor: user.isActive ? Colors.red : Colors.green,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري $action المستخدم...');
        
        // TODO: استدعاء API لتبديل حالة المستخدم
        await Future.delayed(const Duration(seconds: 1)); // محاكاة
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم $action المستخدم بنجاح',
        );
        
        _loadUsers();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في $action المستخدم: $e',
        );
      }
    }
  }

  /// حذف المستخدم
  Future<void> _deleteUser(User user) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'حذف المستخدم',
      message: 'هل أنت متأكد من حذف المستخدم "${user.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
      confirmText: 'حذف',
      icon: Icons.delete,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري حذف المستخدم...');
        
        // TODO: استدعاء API لحذف المستخدم
        await Future.delayed(const Duration(seconds: 1)); // محاكاة
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم حذف المستخدم بنجاح',
        );
        
        _loadUsers();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حذف المستخدم: $e',
        );
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
