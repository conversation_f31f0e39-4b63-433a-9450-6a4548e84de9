name: flutter_application_2
description: "نظام إدارة المهام - تطبيق لإدارة المهام والمشاريع"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=2.19.0 <3.8.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management
  get: ^4.6.6

  # API and HTTP
  http: ^1.1.0
  dio: ^5.4.0
  connectivity_plus: ^6.1.4
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  path: ^1.8.3

  # UI components
  flutter_slidable: ^4.0.0
  file_picker: ^8.0.0+1
  image_picker: ^1.0.7
  intl: #^0.19.0  # Commented out due to compatibility issues with Syncfusion Calendar
  timeline_tile: ^2.0.0
  emoji_picker_flutter: ^2.0.0
  geolocator: ^11.0.0
  flutter_colorpicker: ^1.1.0
  # drag_and_drop_lists: 0.3.2  # Commented out due to compatibility issues

  # مكتبات بديلة للرسوم البيانية والتقويم - تم تعليقها لاستخدام Syncfusion
  # fl_chart: 0.71.0  # مكتبة رسوم بيانية بديلة لـ Syncfusion Charts - معلقة لاستخدام Syncfusion Charts

  # مكتبة تقويم بديلة لـ Syncfusion Calendar
  table_calendar: ^3.0.9  # مكتبة تقويم خفيفة وقابلة للتخصيص
  flutter_calendar_carousel: ^2.4.2  # مكتبة تقويم مع دعم للعرض الشهري والأسبوعي

  # مكتبة جداول بيانات بديلة لـ Syncfusion DataGrid
  data_table_2: ^2.5.10  # تحسين لمكتبة DataTable الأصلية من Flutter
  pluto_grid: ^8.0.0  # مكتبة جداول بيانات متقدمة مع دعم التصدير المدمج

  # مكتبات التصدير والاستيراد المتقدمة
  csv: ^5.0.1  # مكتبة للتعامل مع ملفات CSV (متوافقة مع pluto_grid_export)
  excel: ^4.0.6  # مكتبة للتعامل مع ملفات Excel
  file_saver: ^0.2.13  # مكتبة لحفظ الملفات

  # مكتبات إضافية لدعم PlutoGrid المتقدم
  flutter_staggered_grid_view: ^0.7.0  # لتحسين عرض الشبكات
  collection: ^1.19.1  # أدوات مجموعات متقدمة

  # مكتبة مؤشرات بديلة لـ Syncfusion Gauges
  percent_indicator: ^4.2.3  # مكتبة لعرض مؤشرات النسب المئوية
  flutter_animation_progress_bar: ^2.3.1  # شريط تقدم متحرك

  # استخدام Slider و RangeSlider الأصليين من Flutter بدلاً من Syncfusion Sliders

  # الاحتفاظ بمكتبة PDF
  syncfusion_flutter_pdf: ^29.1.33  # إنشاء وتحرير ملفات PDF

  # مكتبات إضافية للتقويم وجدولة المهام

  # Utilities
  uuid: ^4.1.0
  equatable: ^2.0.5
  package_info_plus: ^8.3.0
  archive: ^3.6.1
  image: ^4.1.7
  photo_view: ^0.14.0

  desktop_drop: ^0.6.0
  mime: ^1.0.5
  url_launcher: ^6.2.5

  # JSON serialization
  json_annotation: ^4.8.1

  # WebSocket
  web_socket_channel: ^2.4.0

  # مكتبات معالجة الملفات
  # ملاحظة: تم تعليق المكتبات غير المستخدمة حالياً لتحسين الأداء وتقليل حجم التطبيق
  # background_downloader: ^8.0.5  # لتنفيذ عمليات الرفع والتنزيل في الخلفية - غير مستخدمة حالياً
  cross_file: ^0.3.3+8  # مستخدمة للتعامل مع الملفات عبر المنصات المختلفة

  # عارضات ومحررات المستندات
  syncfusion_flutter_pdfviewer: ^29.1.33  # مستخدمة لعرض ملفات PDF
  printing: ^5.11.1  # لطباعة المستندات
  # محرر نصوص متقدم مثل Microsoft Word
  flutter_quill: ^10.8.5  # محرر نصوص غني مع جميع الميزات المتقدمة
  # image_editor: ^1.3.0  # لتعديل الصور - غير مستخدمة حالياً
  # extended_image: ^8.2.0  # لتحسين عرض الصور - غير مستخدمة حالياً

  # Reports and exports
  pdf: ^3.11.3
  # printing: ^5.14.2
  open_file: ^3.5.10
  share_plus: ^7.2.1

  # تحسين دعم اللغة العربية في التقارير
  flutter_html_to_pdf: ^0.7.0   # تحويل HTML إلى PDF مع دعم أفضل للغة العربية
  arabic_font: ^0.0.9  # خطوط عربية للتقارير
  flutter_pdfview: ^1.3.2  # عرض ملفات PDF مع دعم أفضل للغة العربية

  # Localization
  flutter_localizations:
    sdk: flutter
  msix: ^3.16.9

  # Chart libraries - مكتبات الرسوم البيانية
  syncfusion_flutter_charts: ^29.1.33  # نسخة متوافقة مع Flutter 3.29.0
  signalr_netcore: ^0.1.7+2-nullsafety.3
  # charts_flutter: ^0.12.0  # تعليق بسبب تضارب مع intl package
  # pie_chart: ^5.4.0  # تم تعليقها لاستخدام Syncfusion Charts فقط

  # مكتبة الإشعارات المحلية
  flutter_local_notifications: ^17.1.2
  mockito: ^5.4.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # أداة لتغيير أيقونة التطبيق
  flutter_launcher_icons: ^0.13.1

  # JSON code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

dependency_overrides:
  # تم إزالة معظم مكتبات Syncfusion واستبدالها بمكتبات بديلة
  syncfusion_flutter_pdf: ^29.1.33  # تحديث مكتبة PDF

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    # خطوط عربية أساسية
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
    
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Cairo-Black.ttf
          weight: 900
        - asset: assets/fonts/Cairo-ExtraLight.ttf
          weight: 200
    
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/Amiri-Italic.ttf
          style: italic
        - asset: assets/fonts/Amiri-BoldItalic.ttf
          weight: 700
          style: italic
    
    - family: CourierPrime
      fonts:
        - asset: assets/fonts/CourierPrime-Regular.ttf
        - asset: assets/fonts/CourierPrime-Bold.ttf
          weight: 700
        - asset: assets/fonts/CourierPrime-Italic.ttf
          style: italic
        - asset: assets/fonts/CourierPrime-BoldItalic.ttf
          weight: 700
          style: italic
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
