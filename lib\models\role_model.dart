import 'package:flutter_application_2/models/role_default_permission_model.dart';
import 'package:flutter_application_2/models/user_model.dart';

/// نموذج الدور (Role) - متوافق مع الباك اند
class Role {
  final int id;
  final String name;
  final String? description;
  final int createdAt;
  final int? updatedAt;

  // العلاقات البرمجية
  final List<RoleDefaultPermission>? defaultPermissions;
  final List<User>? users;

  const Role({
    required this.id,
    required this.name,
    this.description,
    required this.createdAt,
    this.updatedAt,
    this.defaultPermissions,
    this.users,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      defaultPermissions: json['defaultPermissions'] != null
          ? (json['defaultPermissions'] as List)
              .map((p) => RoleDefaultPermission.fromJson(p as Map<String, dynamic>))
              .toList()
          : null,
      users: json['users'] != null
          ? (json['users'] as List)
              .map((u) => User.fromJson(u as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'defaultPermissions': defaultPermissions?.map((p) => p.toJson()).toList(),
      'users': users?.map((u) => u.toJson()).toList(),
    };
  }

  /// التحقق من أن الدور هو مدير أو أعلى
  bool get isManagerOrAbove {
    final roleName = name.toLowerCase();
    return roleName.contains('مدير') || 
           roleName.contains('admin') || 
           roleName.contains('manager') || 
           roleName.contains('supervisor') || 
           roleName.contains('مشرف');
  }
}
