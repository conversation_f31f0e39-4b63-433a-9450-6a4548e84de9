import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/text_document_model.dart';
import '../services/api/text_document_api_service.dart';

/// متحكم المستندات النصية
class TextDocumentController extends GetxController {
  final TextDocumentApiService _apiService = TextDocumentApiService();

  // قوائم المستندات
  final RxList<TextDocument> _allDocuments = <TextDocument>[].obs;
  final RxList<TextDocument> _filteredDocuments = <TextDocument>[].obs;
  final RxList<TextDocument> _recentDocuments = <TextDocument>[].obs;
  final RxList<TextDocument> _sharedDocuments = <TextDocument>[].obs;

  // المستند الحالي
  final Rx<TextDocument?> _currentDocument = Rx<TextDocument?>(null);

  // متحكم النص للتوافق مع الكود الموجود
  final Rx<TextEditingController?> _textController = Rx<TextEditingController?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<TextDocumentType?> _selectedTypeFilter = Rx<TextDocumentType?>(null);
  final RxBool _showSharedOnly = false.obs;

  // Getters
  List<TextDocument> get allDocuments => _allDocuments;
  List<TextDocument> get filteredDocuments => _filteredDocuments;
  List<TextDocument> get recentDocuments => _recentDocuments;
  List<TextDocument> get sharedDocuments => _sharedDocuments;
  TextDocument? get currentDocument => _currentDocument.value;
  TextEditingController? get textController => _textController.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  TextDocumentType? get selectedTypeFilter => _selectedTypeFilter.value;
  bool get showSharedOnly => _showSharedOnly.value;

  // للتوافق مع الكود الموجود - إرجاع Rx objects
  Rx<TextDocument?> get currentDocumentRx => _currentDocument;
  Rx<TextEditingController?> get textControllerRx => _textController;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// تهيئة المتحكم
  Future<void> _initializeController() async {
    try {
      await loadAllDocuments();
      await loadRecentDocuments();
    } catch (e) {
      debugPrint('خطأ في تهيئة متحكم المستندات: $e');
    }
  }

  /// تحميل جميع المستندات
  Future<void> loadAllDocuments() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documents = await _apiService.getAllDocuments();
      _allDocuments.assignAll(documents);
      _applyFilters();
      debugPrint('تم تحميل ${documents.length} مستند');
    } catch (e) {
      _error.value = 'خطأ في تحميل المستندات: $e';
      debugPrint('خطأ في تحميل المستندات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مستند بواسطة المعرف
  Future<void> loadDocumentById(String id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentId = int.tryParse(id);
      if (documentId == null) {
        _error.value = 'معرف المستند غير صحيح';
        return;
      }
      final document = await _apiService.getDocumentById(documentId);
      if (document != null) {
        _currentDocument.value = document;
        // تهيئة متحكم النص
        _textController.value = TextEditingController(text: document.getDocumentText());
        debugPrint('تم تحميل المستند: ${document.title}');
      } else {
        _error.value = 'المستند غير موجود';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل المستند: $e';
      debugPrint('خطأ في تحميل المستند: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المستندات المرتبطة بمهمة
  Future<void> loadDocumentsByTaskId(String taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final taskIdInt = int.tryParse(taskId);
      if (taskIdInt == null) {
        _error.value = 'معرف المهمة غير صحيح';
        return;
      }
      final documents = await _apiService.getDocumentsByTaskId(taskIdInt);
      _allDocuments.assignAll(documents);
      _applyFilters();
      debugPrint('تم تحميل ${documents.length} مستند للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مستندات المهمة: $e';
      debugPrint('خطأ في تحميل مستندات المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المستندات الحديثة
  Future<void> loadRecentDocuments() async {
    try {
      final documents = await _apiService.getRecentDocuments();
      _recentDocuments.assignAll(documents);
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة: $e');
    }
  }

  /// تحميل المستندات المشتركة
  Future<void> loadSharedDocuments() async {
    try {
      final documents = await _apiService.getSharedDocuments();
      _sharedDocuments.assignAll(documents);
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة: $e');
    }
  }

  /// البحث في المستندات
  Future<void> searchDocuments(String query) async {
    _searchQuery.value = query;
    
    if (query.isEmpty) {
      _applyFilters();
      return;
    }

    _isLoading.value = true;
    try {
      final documents = await _apiService.searchDocuments(query);
      _filteredDocuments.assignAll(documents);
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      _applyFilters(); // العودة للمرشحات المحلية في حالة الخطأ
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مستند جديد
  Future<bool> createDocument({
    required String title,
    required TextDocumentType type,
    String? taskId,
    bool isShared = false,
    String? content,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentContent = content ?? _textController.value?.text ?? '';
      final taskIdInt = taskId != null ? int.tryParse(taskId) : null;
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      final document = TextDocument(
        id: 0, // سيتم تعيينه من الخادم
        title: title,
        fileName: '${title.replaceAll(' ', '_')}.txt',
        filePath: '', // سيتم تعيينه من الخادم
        fileType: 'text/plain',
        fileSize: documentContent.length,
        content: TextDocument.textToJson(documentContent),
        type: type,
        taskId: taskIdInt,
        isShared: isShared,
        uploadedBy: Get.find<AuthController>().currentUser.value?.id ?? 1,
        uploadedAt: timestamp,
        createdBy: Get.find<AuthController>().currentUser.value?.id ?? 1,
        createdAt: timestamp,
        updatedAt: timestamp,
      );

      final newDocument = await _apiService.createDocument(document);
      if (newDocument != null) {
        _allDocuments.add(newDocument);
        _currentDocument.value = newDocument;
        _applyFilters();
        debugPrint('تم إنشاء المستند: ${newDocument.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المستند: $e';
      debugPrint('خطأ في إنشاء المستند: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مستند
  Future<bool> updateDocument({
    required String id,
    String? title,
    TextDocumentType? type,
    bool? isShared,
    String? content,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentId = int.tryParse(id);
      if (documentId == null) {
        _error.value = 'معرف المستند غير صحيح';
        return false;
      }

      // البحث عن المستند الحالي
      final currentDoc = _allDocuments.firstWhereOrNull((doc) => doc.id == documentId);
      if (currentDoc == null) {
        _error.value = 'المستند غير موجود';
        return false;
      }

      final documentContent = content ?? _textController.value?.text;
      final updatedDocument = currentDoc.copyWith(
        title: title,
        content: documentContent != null ? TextDocument.textToJson(documentContent) : null,
        type: type,
        isShared: isShared,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final result = await _apiService.updateDocument(updatedDocument);
      if (result != null) {
        final index = _allDocuments.indexWhere((doc) => doc.id == documentId);
        if (index != -1) {
          _allDocuments[index] = result;
        }
        _currentDocument.value = result;
        _applyFilters();
        debugPrint('تم تحديث المستند: ${result.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المستند: $e';
      debugPrint('خطأ في تحديث المستند: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مستند
  Future<bool> deleteDocument(String id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentId = int.tryParse(id);
      if (documentId == null) {
        _error.value = 'معرف المستند غير صحيح';
        return false;
      }
      final success = await _apiService.deleteDocument(documentId);
      if (success) {
        _allDocuments.removeWhere((doc) => doc.id == documentId);
        if (_currentDocument.value?.id == documentId) {
          _currentDocument.value = null;
          _textController.value = null;
        }
        _applyFilters();
        debugPrint('تم حذف المستند');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المستند: $e';
      debugPrint('خطأ في حذف المستند: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = List<TextDocument>.from(_allDocuments);

    // تطبيق مرشح النوع
    if (_selectedTypeFilter.value != null) {
      filtered = filtered.where((doc) => doc.type == _selectedTypeFilter.value).toList();
    }

    // تطبيق مرشح المشاركة
    if (_showSharedOnly.value) {
      filtered = filtered.where((doc) => doc.isShared).toList();
    }

    // تطبيق مرشح البحث المحلي
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((doc) =>
          doc.title.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          doc.getDocumentText().toLowerCase().contains(_searchQuery.value.toLowerCase())
      ).toList();
    }

    _filteredDocuments.assignAll(filtered);
  }

  /// تعيين مرشح النوع
  void setTypeFilter(TextDocumentType? type) {
    _selectedTypeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح المشاركة
  void setSharedFilter(bool showSharedOnly) {
    _showSharedOnly.value = showSharedOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _selectedTypeFilter.value = null;
    _showSharedOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تعيين متحكم النص
  void setTextController(TextEditingController? controller) {
    _textController.value = controller;
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllDocuments(),
      loadRecentDocuments(),
      loadSharedDocuments(),
    ]);
  }

  @override
  void onClose() {
    _textController.value?.dispose();
    super.onClose();
  }
}
